package com.upex.reconciliation.service.common.constants;


import com.upex.reconciliation.service.model.alarm.AlarmTemplateModel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateLevelEnum.EMERGENCY;
import static com.upex.reconciliation.service.common.constants.AlarmTemplateLevelEnum.NORMAL;
import static com.upex.reconciliation.service.common.constants.AlarmTemplateTypeEnum.LOG;

@Getter
@AllArgsConstructor
public enum AlarmTemplateEnum {
    NONE("none", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, ""),
    INNER_TRANSFER_ALARM("inner_transfer_alarm", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "内部转账报警：❗❗❗ \n" +
            "标题(title): 内部转账+bgPay报警 \n" +
            "开始时间(startTime): ${startTime?number_to_datetime?string(\"yyyy-MM-dd HH:mm:ss.SSS\")!''} \n" +
            "结束时间(endTime): ${endTime?number_to_datetime?string(\"yyyy-MM-dd HH:mm:ss.SSS\")!''} \n" +
            "<#if resultList?? && (resultList?size> 0)> \n" +
            "<#list resultList as resultItem> \n" +
            "        事务id(txId): ${resultItem.txId!''} \n" +
            "        币种id(coinId): ${resultItem.coinId!''} \n" +
            "        redis分区号(partition): ${resultItem.partition!''} \n" +
            "        提现订单号(withdrawalOrderId): ${resultItem.withdrawalOrderId!''} \n" +
            "        提现订单金额(withdrawalAmount): ${resultItem.withdrawalAmount!''} \n" +
            "        充值订单号(rechargeOrderId): ${resultItem.rechargeOrderId!''} \n" +
            "        充值订单金额(rechargeAmount): ${resultItem.rechargeAmount!''} \n" +
            "\n" +
            "</#list> \n" +
            "</#if> \n" +
            "<#if atUserIdList?? && (atUserIdList?size> 0)><#list atUserIdList as atUserId><at user_id='${atUserId}'></at></#list></#if>"),
    HOT_RESTART("hot_restart", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "重启业务线成功，业务线:{}"),
    CREATE_TABLE_SUCCESS("create_table_success", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "定时任务创建表成功！"),
    CREATE_TABLE_BYROUTE_SUCCESS("create_table_byroute_success", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "定时任务根据路由配置创建表成功！"),
    CREATE_TABLE_ERROR("create_table_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "定时任务创建表失败，请及时排查问题!"),
    CREATE_TABLE_BYROUTE_ERROR("create_table_byroute_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "定时任务根据路由配置创建表失败，请及时排查问题!"),
    //CREATE_TABLE_COIN_TYPE_USER_SUCCESS("create_table_coin_type_user_success", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.NORMAL, "定时任务创建表BillCoinTypeUser成功！"),
    //CREATE_TABLE_COIN_TYPE_USER_ERROR("create_table_coin_type_user_error", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.NORMAL, "定时任务创建表BillCoinTypeUser失败，请及时排查问题!"),
    CLEAN_UP_INACTIVE_USERS_ERROR("clean_up_inactive_users_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "定时任务清理非活跃用户失败，请及时排查问题!"),
    DELETE_HISTORY_DATA_SUCCESS("delete_history_data_success", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "定时任务删除历史数据成功！"),
    //WATCH_MEMORY_METRICS("watch_memory_metrics", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.NORMAL, "业务线对账延迟告警 accountType={} nowTime={} checkOkTime={}!"),
    //ALARM_ERROR_BILL_MAP("alarm_error_bill_map", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.NORMAL, "个人对账errorMap过大，请及时查看 accountType={} errorBillMapSize={}!"),
    ALARM_TIME_SLICE_DELAY_MSG("alarm_time_slice_delay_msg", LOG, AlarmTemplateQpsEnum.ONE_MINUTE, EMERGENCY, "消息时间小于当前对账通过时间片时间，请即使关注！ accountType={} timeOffset={} checkOkTime={} bizTime={} msg={}!"),
    ALARM_USER_PROPERTY_MAP("alarm_user_property_map", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "个人对账userPropertyMap过大，请及时查看 accountType={} userPropertyMapSize={}!"),
    //ALARM_RATE_LIMIT("alarm_rate_limit", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.NORMAL, "个人对账触发流控，请及时查看 accountType={} errorBillMapSize={} startTimeSliceMapSize={}!"),
    CHECK_DATA_CONTINUOUS("check_data_continuous", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "比对逻辑执行完毕，最新比对时间:{}，当前有如下业务线:{} 还未对到时刻:{} ,对账进度过慢请关注!"),
    ASSETS_BILL_CHECK("assets_bill_check", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "总账对账执行结果失败，请即使关注，对账时间： {} ，业务线：{}!"),
    TRANSFER_SERVICE_ERROR("transfer_service_error", LOG, AlarmTemplateQpsEnum.ONE_SECOND, NORMAL, "调用动账接口失败，请及时关注！❗❗❗ 业务线：{}"),
    //INCOME_SCENE3_DATA_PROCESS("income_scene3_data_process", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.NORMAL, "财务收入对账，业务线数据未准备好，请即使关注，对账时间： {} ，业务线：{}!"),
    INCOME_SCENE3_CHECK("income_scene3_check", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "财务收入情况每日更新\n{}"),
    FEE_SCENE_CHECK("fee_scene_check", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "手续费对账对账报警：❗❗❗\n业务线:{}  \n币种:{} \n时间:{} \n手续费:{} \n成交额:{} \n比值:{} \n最小阈值:{}  \n最大阈值:{}\n公式: 最小阈值<=手续费/成交额<=最大阈值 "),
    COMMISSION_SCENE_CHECK("commission_scene_check", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "返佣对账报警：❗❗❗\n币种:{} \n时间:{} \n返佣额:{} \n手续费:{} \n比值:{} \n最小阈值:{}  \n最大阈值:{}\n公式: 最小阈值<=返佣额/手续费<=最大阈值 "),

    USER_CHECK_MODULE_ABANDON_MESSAGE_ERROR("user_check_module_abandon_message_error", LOG, AlarmTemplateQpsEnum.ONE_MINUTE, NORMAL, "个人对账丢失延迟消息告警：❗❗❗ accountType:{} messageId:{} bizTIme:{}"),
    TIME_SLICE_BILL_CHECK_ERROR("time_slice_bill_check_error", LOG, AlarmTemplateQpsEnum.FIVE_MINUTE, AlarmTemplateLevelEnum.EMERGENCY, "时间片对账失败，请即使关注日志！accountType={} timeSliceKey={}!"),
    DO_BATCH_CHECK_PROPERTY_SUCCESS("do_batch_check_property_success", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "聚批对账成功，触发自动修数逻辑，请及时关注！ accountType:{} accountUniqueId:{}"),
    USER_CHECK_MODULE_TAKE_COMMAND_ERROR("user_check_module_take_command_error", LOG, AlarmTemplateQpsEnum.ONE_MINUTE, EMERGENCY, "个人对账抛异常，请及时关注！ accountType:{}"),
    USER_PROFIT_MODULE_TAKE_COMMAND_ERROR("user_profit_module_take_command_error", LOG, AlarmTemplateQpsEnum.ONE_MINUTE, EMERGENCY, "个人profit消息抛异常，请及时关注！ accountType:{}"),
    USER_CHECK_MODULE_ORDER_DELAY_ERROR("user_check_module_order_delay_error", LOG, AlarmTemplateQpsEnum.ONE_MINUTE, NORMAL, "个人对账相同orderId消息时间乱序，请及时关注！ accountType:{} orderId={} firstOrderTime={} lastOrderTime={}"),
    FLOW_CHECK_MODULE_TAKE_COMMAND_ERROR("flow_check_module_take_command_error", LOG, AlarmTemplateQpsEnum.FIVE_MINUTE, EMERGENCY, "流水对账抛异常，请及时关注！ accountType:{}"),
    TIME_SLICE_CHECK_MODULE_TAKE_COMMAND_ERROR("time_slice_check_module_take_command_error", LOG, AlarmTemplateQpsEnum.ONE_MINUTE, AlarmTemplateLevelEnum.GENERAL, "时间片增量计算流水抛异常，请及时关注！ accountType:{}"),
    CHECK_SYMBOL_PROPERTY_ERROR("check_symbol_property_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "合约symbol维度数据对比异常，请及时关注日志！ accountType:{}"),
    CHECK_CONTRACT_PROFIT_PROPERTY_ERROR("check_contract_profit_property_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "合约contract profit维度数据对比异常，请及时关注日志！ accountType:{}"),
    DATA_PERSISTENCE_ERROR("data_persistence_error", LOG, AlarmTemplateQpsEnum.FIVE_MINUTE, EMERGENCY, "xxl-job存盘系统异常，请及时关注日志！ accountType:{} params:{}"),
    //ALL_CHECK_START_ERROR("all_check_start_error", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.NORMAL, "xxl-job internal对账系统异常，请及时关注日志！ params:{}"),
    LEVER_SPOT_CHECK_START_ERROR("lever_spot_check_start_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "xxl-job leverspot对账系统异常，请及时关注日志！ params:{}"),
    BUSINESS_CHECK_START_ERROR("business_check_start_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "xxl-job 业务线对账系统异常，请及时关注日志！ accountType:{} params:{}"),
    CHECK_NEGATIVE_ERROR("check_negative_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "负值检测报警：❗❗❗\n" +
            " 业务线：(${accountType!''}) \n" +
            " userCoin:${model.userCoinKey!''} \n" +
            " 告警类型: <#if (alarmType?? && alarmType?trim !=\"\")>超过白名单阈值（${alarmType!''}）<#else>默认负值告警（不在白名单中）</#if> \n" +
            " 是否系统用户: <#if isSysUser>是, 类型描述: ${sysUserDisplayStr!''}<#else>否</#if>\n" +
            " 用户总资产：${model.userTotalAssets.total!''}（资产:${model.userTotalAssets.propValue!''}，未实现:${model.userTotalAssets.unRealized!''}）\n" +
            " 用户负资产总值：${model.userNegativeSum.total!''}（资产:${model.userNegativeSum.propValue!''}，未实现:${model.userNegativeSum.unRealized!''}）\n" +
            " <#if isCoin>\n" +
            " 币种资产：${model.totalValue!''}（资产:${model.propValue!''}，未实现:${model.unRealized!''}）\n" +
            " </#if> \n" +
            " 负值出现时间:${model.errorTimeStr!''} \n" +
            " 负值检测时间:${model.checkTimeStr!''} \n" +
            "<#if atUserIdList?? && (atUserIdList?size> 0)><#list atUserIdList as atUserId><at user_id='${atUserId}'></at></#list></#if>"),
    CHECK_NOT_PAPTRADING_ENV_BY_USER_ID_ERROR("check_paptrading_by_user_id_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "非模拟盘环境出现模拟盘用户流水告警：❗❗❗ 业务线：({}) 业务类型:{} 用户ID:{} 消息ID:{}"),
    CHECK_PAPTRADING_ENV_BY_USER_ID_ERROR("check_paptrading_by_user_id_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "模拟盘环境非模拟盘用户流水告警：❗❗❗ 业务线：({}) 业务类型:{} 用户ID:{} 消息ID:{}"),
    CHECK_FLOW_BIZ_TYPE_ERROR("check_flow_biz_type_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "非法业务类型告警：❗❗❗ 业务线：({}) 业务类型:{} 消息Id:{}"),
    LEVER_CHECK_NEGATIVE_ERROR("lever_check_negative_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "杠杆负值检测报警：❗❗❗ 业务线：({}) userId:{} 负值检测时间:{} data:{}"),
    ///CHECK_NEGATIVE_M_PRICE_ERROR("check_negative_m_price_error", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.NORMAL, "负值检测mPrice为空报警：❗❗❗ 业务线：({}) symbolId:{}"),
    //CHECK_NEGATIVE_S_PRICE_ERROR("check_negative_m_price_error", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.NORMAL, "负值检测sPrice为空报警：❗❗❗ 业务线：({}) coinId:{}"),
    //CHECK_NEGATIVE_USDT_PRICE_ERROR("check_negative_usdt_price_error", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.NORMAL, "负值检测usdtPrice为空报警：❗❗❗ 业务线：({}) coinId:{}"),
    KAFKA_POLL_CONSUMER_ERROR("kafka_poll_consumer_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "kafka消费消息错误，请及时关注：❗❗❗❗❗❗ 业务线：({}) partition:{}"),
    KAFKA_POLL_PROFIT_CONSUMER_ERROR("kafka_poll_profit_consumer_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "kafka-profit消费消息错误，请及时关注：❗❗❗❗❗❗ 业务线：({}) partition:{}"),
    USER_ASSET_CHECK_OFFER_COMMAND_ERROR("user_asset_check_offer_command_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "BillUserAssetCheckModule.offerCommand消息放入队列失败：❗❗❗❗❗❗ 业务线：({}) "),
    USER_WITHDRAWAL_CHECK_PROFIT_ERROR("user_withdrawal_check_profit_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "提币盈利检测告警明细! 用户id：{}\n {}"),

    KAFKA_CONSUMER_TOPIC_ERROR("kafka_consumer_topic_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "kafka消费topic异常：❗❗❗❗❗❗ 业务线：({}) topic:{} errorTopic:{} patition:{}"),
    //KAFKA_DECODER_BIZ_TYPE_NOT_EXIST("kafka_decoder_biz_type_not_exist", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.NORMAL, "kafka消息解析，bizType不存在：❗❗❗ 业务线：({}) bizType:{} bizId:{}"),
    KAFKA_CONSUMER_UPDATE_DATA("kafka_consumer_update_data", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.NORMAL, "监听到业务线流水更新消息，请及时关注！ accountType:{} userId:{} bizId:{}"),
    KAFKA_CONSUMER_UPDATE_DATA_FORBIDDEN("kafka_consumer_update_data_forbidden", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.NORMAL, "监听到业务线流水更新消息,触发提币阻断，请及时关注！ accountType:{} userId:{}"),
    KAFKA_CONSUMER_DELETE_DATA("kafka_consumer_delete_data", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.NORMAL, "监听到业务线流水删除消息,触发提币阻断，请及时关注！ accountType:{} userId:{} bizId:{}"),
    LEDGER_CHECK_START_ERROR("ledger_check_start_error", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.NORMAL, "xxl-job 总账对账系统异常，请及时关注日志！ params:{}"),
    LEDGER_KAFKA_POLL_CONSUMER_ERROR("kafka_poll_consumer_error", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.NORMAL, "总账kafka消费消息错误，请及时关注：❗❗❗❗❗❗ 业务线：({}) partition:{}"),
    LEDGER_KAFKA_POLL_CONSUMER_ERROR_START_NODE_ISNULL("ledger_kafka_poll_consumer_error_start_node_is_null", LOG, AlarmTemplateQpsEnum.ONE_MINUTE, AlarmTemplateLevelEnum.NORMAL, "总账kafka消费消息错误，开始节点为空，消息乱序造成消息丢失请及时关注：❗❗❗❗❗❗ 业务线：({}) checkTime:{} partition:{} billLedgerTimeSliceDTOIsNull:{} msg:{}"),
    EMERGENCY_MESSAGE("emergency_message", LOG, AlarmTemplateQpsEnum.NONE, EMERGENCY, "{} 触发电话告警，请及时关注日志！"),
    LEDGER_CHECK_OK_TIME_DELAY("ledger_check_ok_time_delay", LOG, AlarmTemplateQpsEnum.FIVE_MINUTE, EMERGENCY, "总账延时报警：❗❗❗ 业务线：({}), nowTime:{}, checkTime:{}"),
    TIME_SLICE_CHECK_OK_TIME_DELAY("time_slice_check_ok_time_delay", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "业务线延时报警：❗❗❗ 业务线：({}), nowTime:{}, checkTime:{}"),
    LEDGER_TIME_SLICE_BILL_CHECK_ERROR("ledger_time_slice_bill_check_error", LOG, AlarmTemplateQpsEnum.FIVE_MINUTE, AlarmTemplateLevelEnum.EMERGENCY, "总账时间片对账失败，请即使关注日志！accountType={} timeSliceKey={}!"),
    LEDGER_TIME_SLICE_TRANSFER_CHECK_ERROR("ledger_time_slice_transfer_check_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "待动账金额检验失败，请即使关注日志！timeSliceKey={}!"),
    ERROR_MAP_DELAY_TIME_ALARM("error_map_delay_time_alarm", LOG, AlarmTemplateQpsEnum.FIVE_MINUTE, AlarmTemplateLevelEnum.EMERGENCY, "errorMap阻塞延时报警：❗❗❗ 业务线：({}), nowTime:{}, bizTime:{}, lastTimeSliceTime:{}"),
    CONTRACT_EXPERIENCE_ACCOUNT_ERROR("contract_experience_account_error", LOG, AlarmTemplateQpsEnum.FIVE_MINUTE, NORMAL, "体验金账户余额不足:❗❗❗\n 系统账户UID:{} \n 业务线:{} \n 影响卡券:{} \n 当前余额:{} \n 报警阈值:{} \n\n"),
    CHECK_IN_AND_OUT_ASSETS_SUCCESS("check_in_and_out_assets_success", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "入出资产对账成功！✅ 业务线:{} 对账时间:{}"),
    CHECK_LCOUNT_SCOUNT_SUCCESS("check_lcount_scount_success", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "多空仓对账成功！✅ 业务线:{} 对账时间:{}"),
    CHECK_ASSETS_TRADING_ON_SUCCESS("check_assets_trading_on_success", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "盈亏对账成功！✅ 业务线:{} 对账时间:{}"),
    PRINT_LOG_FOR_SLOW_INTERFACE_ALARM("print_log_for_slow_interface_alarm", LOG, AlarmTemplateQpsEnum.ONE_SECOND, NORMAL, "慢日志告警：❗❗❗\nkey:{}\n耗时(ms):{}\n用户Id:{}\n业务类型:{}\n"),
    USER_NOT_EXIST("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "对账禁止提币报警：\n    用户：{}禁止提币，原因：用户不存在，{}"),
    NOT_CALCULATE_EXCHANGE_PROFIT_TRANSFER_ERROR("not_calculate_exchange_profit_transfer_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "总账盈亏动账检测，本次盈亏换汇数据不动账，请及时关注！checkTime:{} 负值仓位标记：{} 盈亏对账标记：{}"),
    USER_SERVICES("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "对账禁止提币报警：\n    用户：{}禁止提币，原因：用户服务禁止用户提币，{}"),
    USER_ORDER_NOT_EXIST("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "对账禁止提币报警：\n    用户：{}禁止提币，原因：用户提现订单不存在，{}"),
    USER_NEGATIVE_ASSETS("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "对账禁止提币报警：\n    用户：{}禁止提币，原因：用户资产为负值，禁止用户提币，{}"),
    USER_REAL_TIME_ASSETS_EXCEPTION("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "对账禁止提币报警：\n    用户：{}禁止提币，原因：查询用户实时资产异常，禁止用户提币，{}"),
    USER_REAL_TIME_ASSETS_NEGATIVE("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "对账禁止提币报警：\n    用户：{}禁止提币，原因：用户(实时)资产为负值，禁止用户提币，{}"),
    BACK_CALCULATION_BILL_USER_EMPTY_ASSET_NOT_EMPTY("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "对账禁止提币报警：\n    用户：{}禁止提币，原因：反算校验失败，期末资产为空，但是业务系统反算结果不为空，{}"),
    BACK_CALCULATION_BILL_USER_ASSET_QUANTITY_NOT_EQUALS("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "对账禁止提币报警：\n    用户：{}禁止提币，原因：对账资产数量({})和业务资产数量({})不相等，{}"),
    BACK_CALCULATION_BILL_USER_NOT_MATCH_ASSET("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "对账禁止提币报警：\n    用户：{}禁止提币，原因：反算校验失败，对账期末值与业务系统反算结果不匹配，{}"),
    USER_DELAY_ACCOUNT("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "对账禁止提币报警：\n    用户：{}禁止提币，原因：用户存在延迟入账数据，{}"),
    //CAPITAL_BILL_PROFIT_WITHDRAW("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.NORMAL, "对账禁止提币报警：\n    用户：{}禁止提币，原因：资产盈利校验不通过，禁止用户提币"),
    PAP_USER_CAN_NOT_WITHDRAW("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, EMERGENCY, "对账禁止提币报警：\n    用户：{}禁止提币，原因：模拟盘账户禁止提币，{}"),
    DEMO_USER_CAN_NOT_WITHDRAW("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, EMERGENCY, "对账禁止提币报警：\n    用户：{}禁止提币，原因：demo账户禁止提币，{}"),
    CAPITAL_BILL_PROFIT_WITHDRAW_DETAIL("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "对账禁止提币报警：\n    用户：{}禁止提币，原因：资产盈利校验不通过，盈利金额：[{}]，期初资产：[{}] ，禁止用户提币，风控检测开关:[{}],推送风控开关:[{}]{}"),
    USER_ILLEGAL_MODIFICATION_BILL_DATA("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "对账禁止提币报警：\n    用户：{}禁止提币，原因：账单数据更新或删除，{}"),
    ACCOUNT_TYPE_CONFIG_NULL("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, EMERGENCY, "对账禁止提币报警：\n    用户：{}禁止提币，原因：最小业务线配置为空，禁止用户提币，{}"),
    ACCOUNT_TYPE_REC_TIMEOUT("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.EMERGENCY, "对账禁止提币报警：\n    用户：{}禁止提币，原因：最小业务线对账配置对账成功时间 < 允许业务线对账延迟时间，禁止用户提币，{}"),
    INTERNAL_CONFIG_NULL("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.EMERGENCY, "对账禁止提币报警：\n    用户：{}禁止提币，原因：总账配置为空，禁止用户提币，{}"),
    INTERNAL_REC_TIMEOUT("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.EMERGENCY, "对账禁止提币报警：\n    用户：{}禁止提币，原因：总账配置对账成功时间 < 允许总账延迟时间，禁止用户提币，{}"),
    CAPITAL_BILL_LAST_CONFIG_NULL("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.EMERGENCY, "对账禁止提币报警：\n    用户：{}禁止提币，原因：最后一次完成资金对账的配置为空，禁止用户提币，{}"),
    CAPITAL_BILL_DIFF_QUOTA("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.EMERGENCY, "对账禁止提币报警：\n    用户：{}禁止提币，原因：对账差额 >= 禁止提币报警值，禁止用户提币，{}"),
    CAPITAL_BILL_CONFIG_NULL("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.EMERGENCY, "对账禁止提币报警：\n    用户：{}禁止提币，原因：资金对账配置为空，禁止用户提币，{}"),
    CAPITAL_BILL_FORBID_WITHDRAW("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.EMERGENCY, "对账禁止提币报警：\n    用户：{}禁止提币，原因：资金对账差额 >= 资金对账阻断提币值，禁止用户提币，{}"),
    WITHDRAW_BILL_PLATFORM_WITHDRAW_BLOCKING("withdraw_bill_platform_withdraw_blocking", LOG, AlarmTemplateQpsEnum.ONE_MINUTE, AlarmTemplateLevelEnum.EMERGENCY, "对账禁止提币报警：\n    原因：对账平台【{}】阻断用户提币数量超过阈值, 阻断人数：{}，阈值：{} 禁止用户提币，{}"),
    WITHDRAW_HOSTING_BALANCE_CONFIG_NULL("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.EMERGENCY, "对账禁止提币报警：\n    用户：{}禁止提币，原因：资金托管对账配置为空，禁止用户提币，{}"),
    WITHDRAW_HOSTING_BALANCE_FAIL("bill_withdrawal_check_alarm", LOG, AlarmTemplateQpsEnum.NONE, AlarmTemplateLevelEnum.EMERGENCY, "对账禁止提币报警：\n    用户：{}禁止提币，原因：资金托管资金差异超过阈值，禁止用户提币，禁止用户提币，渠道: [{}],capitalDiff: [{}],minDiff: [{}],maxDiff: [{}]，{}"),
    WATCH_PROFIT_TRANSFER_DELAY_ALARM("watch_profit_transfer_delay_alarm", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "动账任务检测异常告警，请及时关注❗❗❗，检测时间：{},动账最小CHECK_OK_TIME：{}"),
    WATCH_PROFIT_TRANSFER_DELAY_ERROR("watch_profit_transfer_delay_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "动账延迟检测任务执行系统异常，请及时关注❗❗❗"),
    WATCH_BILL_CHECK_CONFIG_FLAG_ERROR("watch_bill_check_config_flag_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "对账开关配置检测任务执行系统异常，请及时关注❗❗❗"),
    WATCH_BILL_CHECK_CONFIG_FLAG_SUCCESS("watch_bill_check_config_flag_success", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "对账开关配置检测任务执行成功，未发现异常！✅✅✅"),
    PROPERTY_INIT_USER_ASSETS_ERROR("property_init_user_assets_error", LOG, AlarmTemplateQpsEnum.ONE_MINUTE, EMERGENCY, "批量异步加载用户异常，请及时关注❗❗❗，业务线：{}"),
    PROPERTY_INIT_USER_NOT_EXISTS("property_init_user_not_exists", LOG, AlarmTemplateQpsEnum.ONE_MINUTE, EMERGENCY, "批量异步加载用户验证用户不存在，请及时关注❗❗❗，业务线：{} 用户ID：{}"),
    TRANSFER_FOR_BILL_PROFIT_INFO("transfer_for_bill_profit_error", LOG, AlarmTemplateQpsEnum.ONE_MINUTE, NORMAL, "动账失败报警,请及时关注❗❗❗❗❗❗❗❗❗\n {}"),
    TRANSFER_FOR_BILL_PROFIT_ERROR("transfer_for_bill_profit_error", LOG, AlarmTemplateQpsEnum.ONE_MINUTE, EMERGENCY, "动账失败报警,请及时关注❗❗❗❗❗❗❗❗❗\n {}"),
    CHECK_SYSTEM_ACCOUNT_ERROR("check_system_account_error", LOG, AlarmTemplateQpsEnum.ONE_MINUTE, NORMAL, "系统账号对比异常❗❗❗，业务线：{} 枚举：{} 配置用户ID：{} 查询用户id：{}"),
    WATCH_BILL_CHECK_CONFIG_FLAG_INFO("watch_bill_check_config_flag_info", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "对账开关配置异常告警，请及时关注：❗❗❗\n" +
            "检测范围：业务线对账、总账对账、提币检测开关、关键xxl-job任务执行延迟、业务线告警总开关、单业务功能告警开关\n" +
            "\n" +
            "<#if resultList?? && (resultList?size> 0)>\n" +
            "<#list resultList as resultItem>\n" +
            "${resultItem}\n" +
            "</#list>\n" +
            "</#if>\n" +
            "<#if atUserIdList?? && (atUserIdList?size> 0)><#list atUserIdList as atUserId><at user_id='${atUserId}'></at></#list></#if>"),
    WATCH_BILL_CHECK_CONFIG_FLAG_WARN("watch_bill_check_config_flag_warn", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "对账开关配置异常告警，请及时关注：❗❗❗\n" +
            "检测范围：业务线对账、总账对账、提币检测开关、关键xxl-job任务执行延迟、业务线告警总开关、单业务功能告警开关\n" +
            "\n" +
            "<#if resultList?? && (resultList?size> 0)>\n" +
            "<#list resultList as resultItem>\n" +
            "${resultItem}\n" +
            "</#list>\n" +
            "</#if>\n" +
            "<#if atUserIdList?? && (atUserIdList?size> 0)><#list atUserIdList as atUserId><at user_id='${atUserId}'></at></#list></#if>"),
    SYSTEM_ASSETS_DISBURSE_OUT_RANGE_TEMPLATE("system_assets_disburse_out_range_template", LOG, AlarmTemplateQpsEnum.FIVE_MINUTE, NORMAL, "系统资产转账金额超出范围报警：❗❗❗\n" +
            "对账公式：abs(出金金额) > 容差值 * 阈值比例\n" +
            "\n" +
            "<#if resultList?? && (resultList?size> 0)>\n" +
            "<#list resultList as resultItem>\n" +
            "标题(title): 系统资产转账金额超出范围报警\n" +
            "开始时间(startTime): ${resultItem.startTime!''}\n" +
            "结束时间(endTime): ${resultItem.endTime!''}\n" +
            "业务线(accountTypeName): ${resultItem.accountTypeName!''}\n" +
            "系统用户(userId): ${resultItem.userId?c}\n" +
            "影响卡券(couponType): ${resultItem.couponType!''}\n" +
            "${resultItem.windowIntervalTime!''}分钟总出金(payAmount): ${resultItem.payAmount!''}\n" +
            "总阈值(alarmBalance): ${resultItem.alarmBalance!''}\n" +
            "报警比例(ratio): ${resultItem.ratio!''}\n" +
            "报警阈值(realAlarmBalance): ${resultItem.realAlarmBalance!''}\n" +
            "\n" +
            "</#list>\n" +
            "</#if>\n" +
            "<#if atUserIdList?? && (atUserIdList?size> 0)><#list atUserIdList as atUserId><at user_id='${atUserId}'></at></#list></#if>"),
    SYSTEM_ASSETS_ASSETS_RATIO_ALARM_TEMPLATE("system_assets_assets_ratio_alarm_template", LOG, AlarmTemplateQpsEnum.FIVE_MINUTE, NORMAL, "系统资产折u比例报警：❗❗❗\n" +
            "对账公式：期末资产 / 期初资产 < 阈值比例\n" +
            "\n" +
            "<#if resultList?? && (resultList?size> 0)>\n" +
            "<#list resultList as resultItem>\n" +
            "标题(title): 系统资产折u比例报警\n" +
            "开始时间(startTime): ${resultItem.startTime!''}\n" +
            "结束时间(endTime): ${resultItem.endTime!''}\n" +
            "系统用户(userId): ${resultItem.userId?c}\n" +
            "期初资产折u(beginAmountU): ${resultItem.beginAmount!''}\n" +
            "期末资产折u(endAmountU): ${resultItem.endAmount!''}\n" +
            "报警比例(ratio): ${resultItem.ratio!''}\n" +
            "报警阈值(calculateRatio): ${resultItem.calculateRatio!''}\n" +
            "\n" +
            "</#list>\n" +
            "</#if>\n" +
            "<#if atUserIdList?? && (atUserIdList?size> 0)><#list atUserIdList as atUserId><at user_id='${atUserId}'></at></#list></#if>"),

    CHECK_IN_AND_OUT_ASSETS_TEMPLATE("check_in_and_out_assets_template", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "入出资产不平报警：❗❗❗\n" +
            "业务线(accountType)：${checkResult.accountTypeDesc!''}\n" +
            "流水时间(checkTime):${checkResult.checkTime!''}\n" +
            "对账公式：总资产-(入+出)+待动账金额 < 容差值\n" +
            "失败次数：${checkResult.counter!''}\n" +
            "\n" +
            "<#if checkResult.resultList?? && (checkResult.resultList?size> 0)>\n" +
            "<#list checkResult.resultList as resultItem>\n" +
            "币种（coinId）：${resultItem.coinId!''}\n" +
            "总资产（total）：${resultItem.total!''}\n" +
            "转入（in）：${resultItem.in!''}\n" +
            "转出（out）：${resultItem.out!''}\n" +
            "待动账金额（unProfitTransfers）：${resultItem.unProfitTransfers!''}\n" +
            "容差值（totalDiffTolerateExitValue）：${resultItem.totalDiffTolerateExitValue!''}\n" +
            "差异（diffBalance）：${resultItem.diffBalance!''}\n" +
            "业务类型(bizTypeMap)：${resultItem.bizTypeMap}\n" +
            "\n" +
            "</#list>\n" +
            "</#if>\n" +
            "<#if atUserIdList?? && (atUserIdList?size> 0)><#list atUserIdList as atUserId><at user_id='${atUserId}'></at></#list></#if>"),
    CHECK_LEDGER_IN_AND_OUT_ASSETS_TEMPLATE("check_ledger_in_and_out_assets_template", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "总账入出资产不平报警：❗❗❗\n" +
            "对账公式：总资产-(入+出)+待动账金额 < 容差值\n" +
            "业务线：${checkResult.accountTypeDesc!''}\n" +
            "对账时间：${checkResult.checkTime!''}\n" +
            "\n" +
            "<#if checkResult.resultList?? && (checkResult.resultList?size> 0)>\n" +
            "<#list checkResult.resultList as resultItem>\n" +
            "币种（coinId）：${resultItem.coinId!''}\n" +
            "总资产（total）：${resultItem.total!''}\n" +
            "转入（in）：${resultItem.in!''}\n" +
            "转出（out）：${resultItem.out!''}\n" +
            "待动账金额（unProfitTransfers）：${resultItem.unProfitTransfers!''}\n" +
            "容差值（totalDiffTolerateExitValue）：${resultItem.totalDiffTolerateExitValue!''}\n" +
            "差异（diffBalance）：${resultItem.diffBalance!''}\n" +
            "业务类型(bizTypeMap)：${resultItem.bizTypeMap}\n" +
            "\n" +
            "</#list>\n" +
            "</#if>\n" +
            "<#if atUserIdList?? && (atUserIdList?size> 0)><#list atUserIdList as atUserId><at user_id='${atUserId}'></at></#list></#if>"),
    CHECK_LEDGER_LCOUNT_SCOUNT_TEMPLATE("check_ledger_lcount_scount_template", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "总账多空仓不平报警：❗❗❗\n" +
            "对账公式：多仓 - 空仓 - 0\n" +
            "业务线：${checkResult.accountTypeDesc!''}\n" +
            "对账时间：${checkResult.checkTime!''}\n" +
            "\n" +
            "<#if checkResult.resultList?? && (checkResult.resultList?size> 0)>\n" +
            "<#list checkResult.resultList as resultItem>\n" +
            "币对（symbolId）：${resultItem.symbolId!''}\n" +
            "多仓（lCount）：${resultItem.lCount!''}\n" +
            "空仓（sCount）：${resultItem.sCount!''}\n" +
            "差异（diff）：${resultItem.diff!''}\n" +
            "\n" +
            "</#list>\n" +
            "</#if>\n" +
            "<#if atUserIdList?? && (atUserIdList?size> 0)><#list atUserIdList as atUserId><at user_id='${atUserId}'></at></#list></#if>"),
    CHECK_LEDGER_CONTRACT_PROFIT_TEMPLATE("check_ledger_contract_profit_template", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "总账盈亏不平报警：❗❗❗\n" +
            "对账公式：已实现 +未实现+业务线初始值+总账初始值 > 对账容差值\n" +
            "业务线：${checkResult.accountTypeDesc!''}\n" +
            "对账时间：${checkResult.checkTime!''}\n" +
            "\n" +
            "<#if checkResult.resultList?? && (checkResult.resultList?size> 0)>\n" +
            "<#list checkResult.resultList as resultItem>\n" +
            "对账描述：${resultItem.desc!''}\n" +
            "币对（symbolId）：${resultItem.symbolId!''}\n" +
            "已实现（realized）：${resultItem.realized!''}\n" +
            "未实现（unRealized）：${resultItem.unRealized!''}\n" +
            "业务线初始值（initValue）：${resultItem.initValue!''}\n" +
            "总账初始值：${resultItem.ledgerProfitCheckInitValue!''}\n" +
            "对账容差值：${resultItem.ledgerProfitToleranceValue!''}\n" +
            "差异(diff1)：${resultItem.diff1!''}\n" +
            "差异(diff2)：${resultItem.diff2!''}\n" +
            "\n" +
            "</#list>\n" +
            "</#if>\n" +
            "<#if atUserIdList?? && (atUserIdList?size> 0)><#list atUserIdList as atUserId><at user_id='${atUserId}'></at></#list></#if>"),
    CHECK_USER_PROPERTY_TEMPLATE("check_user_property_template", LOG, AlarmTemplateQpsEnum.ONE_MINUTE, NORMAL, "个人资产对账失败：❗❗❗\n" +
            "业务线(accountType)：${checkResult.accountTypeDesc!''}\n" +
            "流水时间(checkTime):${checkResult.checkTime!''}\n" +
            "对账公式：期初+变动=期末\n" +
            "\n" +
            "<#if checkResult.resultList?? && (checkResult.resultList?size> 0)>\n" +
            "<#list checkResult.resultList as resultItem>\n" +
            "用户(userId)：${resultItem.userId!''}\n" +
            "用户(coinId)：${resultItem.coinId!''}\n" +
            "期初：${resultItem.beforeProp!''}\n" +
            "变动：${resultItem.changeProp!''}\n" +
            "期末：${resultItem.afterProp!''}\n" +
            "流水Id(bizId):${resultItem.bizId!''}\n" +
            "异常字段(prop)：${resultItem.field!''}\n" +
            "\n" +
            "</#list>\n" +
            "</#if>\n" +
            "<#if atUserIdList?? && (atUserIdList?size> 0)><#list atUserIdList as atUserId><at user_id=\"${atUserId}\"></at></#list></#if>"),
    CHECK_LCOUNT_SCOUNT_TEMPLATE("check_lcount_scount_template", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "混合合约多空仓对帐报警：❗❗❗\n" +
            "业务线(accountType)：${checkResult.accountTypeDesc!''}\n" +
            "流水时间(checkTime):${checkResult.checkTime!''}\n" +
            "对账公式：多仓-空仓=0\n" +
            "失败次数：${checkResult.counter!''}\n" +
            "\n" +
            "<#if checkResult.resultList?? && (checkResult.resultList?size> 0)>\n" +
            "<#list checkResult.resultList as resultItem>\n" +
            "交易对（symbolId）：${resultItem.symbolId!''}\n" +
            "多仓（lCount）：${resultItem.lCountTotal!''}\n" +
            "空仓（sCount）：${resultItem.sCountTotal!''}\n" +
            "结果（多仓-空仓）：${resultItem.diffBalance!''}\n" +
            "\n" +
            "</#list>\n" +
            "</#if>\n" +
            "<#if atUserIdList?? && (atUserIdList?size> 0)><#list atUserIdList as atUserId><at user_id=\"${atUserId}\"></at></#list></#if>"),
    CHECK_ASSETS_TRADING_ON_TEMPLATE("check_assets_trading_on_template", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "混合合约盈亏对帐报警：❗❗❗\n" +
            "业务线(accountType)：${checkResult.accountTypeDesc!''}\n" +
            "流水时间(checkTime):${checkResult.checkTime!''}\n" +
            "对账公式：已实现+未实现+初始值<预值\n" +
            "失败次数：${checkResult.counter!''}\n" +
            "\n" +
            "<#if checkResult.resultList?? && (checkResult.resultList?size> 0)>\n" +
            "<#list checkResult.resultList as resultItem>\n" +
            "交易对（symbolId）：${resultItem.symbolId!''}\n" +
            "未实现（unrealized）：${resultItem.unRealized!''}\n" +
            "已实现（realized）：${resultItem.realized!''}\n" +
            "结果（result）：${resultItem.diffBalance!''}\n" +
            "初始值（init）：${resultItem.initValue!''}\n" +
            "标记价（mPrice）：${resultItem.mPrice!''}\n" +
            "容差值（toleranceValue）：${resultItem.tradingOnToleranceValue!''}\n" +
            "\n" +
            "</#list>\n" +
            "</#if>\n" +
            "<#if atUserIdList?? && (atUserIdList?size> 0)><#list atUserIdList as atUserId><at user_id=\"${atUserId}\"></at></#list></#if>"),

    RECON_EXCEPTION_ALARM_TEMPLATE("recon_exception_alarm_template", LOG, AlarmTemplateQpsEnum.ONE_MINUTE, NORMAL, "对账异常报警：❗❗❗\n异常信息：{}"),

    CHECK_CONVERT_RECON_ORDER_ERROR("check_convert_recon_order_error", LOG, AlarmTemplateQpsEnum.NONE, NORMAL, "用户闪兑订单对账异常❗❗❗\n" +
            "任务描述：{}\n" +
            "订单ID: {} \n" +
            "用户ID: {} \n" +
            "系统账户ID: {} \n" +
            "错误信息: {}\n" +
            "订单对账异常，请及时处理！"),

    ;

    /***模版分类code，可以重复，code用于区分不同群消息和告警人***/
    private String code;
    private AlarmTemplateTypeEnum type;
    /***0 不限制***/
    private AlarmTemplateQpsEnum qpsEnum;
    /***0 不限制***/
    private AlarmTemplateLevelEnum level;
    /***模板***/
    private String text;
    private static final Map<String, AlarmTemplateModel> alarmTemplateModelMap = new ConcurrentHashMap<>();

    static {
        for (AlarmTemplateEnum alarmTemplateEnum : AlarmTemplateEnum.values()) {
            AlarmTemplateModel alarmTemplateModel = new AlarmTemplateModel();
            alarmTemplateModel.setName(alarmTemplateEnum.name());
            alarmTemplateModel.setCode(alarmTemplateEnum.getCode());
            alarmTemplateModel.setType(alarmTemplateEnum.getType());
            alarmTemplateModel.setQpsEnum(alarmTemplateEnum.getQpsEnum());
            alarmTemplateModel.setLevel(alarmTemplateEnum.getLevel());
            alarmTemplateModel.setText(alarmTemplateEnum.getText());
            alarmTemplateModelMap.put(alarmTemplateEnum.name(), alarmTemplateModel);
        }
    }

    public static AlarmTemplateEnum getAlarmTemplateEnum(String code) {
        for (AlarmTemplateEnum alarmTemplateEnum : AlarmTemplateEnum.values()) {
            if (alarmTemplateEnum.getCode().equals(code)) {
                return alarmTemplateEnum;
            }
        }
        return null;
    }

    public static AlarmTemplateModel getAlarmTemplateModel(String name) {
        return alarmTemplateModelMap.get(name);
    }

    /**
     * 注册消息模板
     *
     * @param name
     * @param text
     */
    public static AlarmTemplateModel registerTemplate(String name, String text) {
        return registerTemplate(name, name, text);
    }

    /**
     * 注册消息模板
     *
     * @param code
     * @param text
     */
    public static AlarmTemplateModel registerTemplate(String name, String code, String text) {
        AlarmTemplateModel alarmTemplateModel = new AlarmTemplateModel();
        alarmTemplateModel.setName(name);
        alarmTemplateModel.setCode(code);
        alarmTemplateModel.setType(LOG);
        alarmTemplateModel.setQpsEnum(AlarmTemplateQpsEnum.NONE);
        alarmTemplateModel.setLevel(NORMAL);
        alarmTemplateModel.setText(text);
        alarmTemplateModelMap.put(alarmTemplateModel.getName(), alarmTemplateModel);
        return alarmTemplateModel;
    }

    /**
     * 注册消息模板
     *
     * @param code
     * @param text
     */
    public static AlarmTemplateModel registerTemplate(String name, String code, String text, AlarmTemplateQpsEnum qpsEnum) {
        var alarmTemplateModel = registerTemplate(name, code, text);
        alarmTemplateModel.setQpsEnum(qpsEnum);
        return alarmTemplateModel;
    }
}
