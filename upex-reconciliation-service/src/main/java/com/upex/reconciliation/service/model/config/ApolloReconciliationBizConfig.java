package com.upex.reconciliation.service.model.config;

import com.google.common.collect.Sets;
import com.upex.reconciliation.service.model.dto.FixKafkaMessageData;
import lombok.Data;

import java.math.BigDecimal;
import java.util.*;

@Data
public class ApolloReconciliationBizConfig {
    // 引擎加载的总开关，开启后，做引擎的加载，引擎加载后，才可以进行后续的资产初始化动作
    private boolean isOpen = true;
    // 重启后，是否从db加载数据到内存的开关
    private boolean toLoadMemoryData = true;
    private Byte accountType;
    private String accountParam = "default";
    private Long checkOkTime;
    /**
     * 时间分片大小，单位 秒
     */
    private Long timeSliceSize = 60L;
    private boolean inOutCheckOpen = false;

    private boolean coinTypeUserCheckOpen = true;

    private boolean saveOptimizeOpen = false;
    private boolean timeSliceCheckOpen = true;
    private boolean saveTimeSliceOpen = true;
    /**
     * 转入
     */
    private List<String> transferIn = new ArrayList<>();
    /**
     * 转出
     */
    private List<String> transferOut = new ArrayList<>();
    private List<CoinComparisonToleranceVo> coinComparisonToleranceList = new ArrayList<>();
    /**
     * 排除转入
     */
    private List<String> excludeTransferIn = new ArrayList<>();
    /**
     * 排除转出
     */
    private List<String> excludeTransferOut = new ArrayList<>();
    /***系统账户手续费账号***/
    private Long systemFeeUserId;
    /***构建合约换汇账户***/
    private Long exchangeUserId;
    /***合约盈亏/换汇动账账户***/
    private Long contractProfitTransferToUId;
    /***合约利息手续费***/
    private Long contractInterestFeeTransferToUId;
    /**
     * 盈亏对帐容忍值
     */
    private BigDecimal tradingOnToleranceValue = BigDecimal.ZERO;
    /***多空仓对账开关***/
    private boolean checkLCountSCountOpen = false;
    /***混合合约下，是否开启盈亏对帐***/
    private boolean checkTradingOn = false;
    // 落盘的sql单次最大插入/查询数据量
    private Integer singleSqlMaxSize = 1000;
    // 存储时，将多少个时间片聚合成一条进行存储
    private Integer mergeTimeSliceSize = 5;
    // 幂等表量比较大，到时候可以关闭落盘逻辑
    private boolean saveUniqueTableOpen = true;
    // 初始化是否进行 远程二方调用的开关
    //private boolean initRpcCallOpen = true;
    /**
     * 初始化时的并发度
     */
    private Integer initPartitionSize = 50;
    // 是否开启init时的日志追踪，会打印所有的远程调用前后的入参和返回
    private boolean initTrackLogOpen = true;
    // 小于等于这个值的log级别才打印日志
    private int logLevelCode = 5;
    private Long billCheckBeforeBizTimeThreshold = 1 * 60 * 1000L;
    // 入出容忍值
    private BigDecimal totalDiffTolerateExitValue = BigDecimal.ZERO;
    /***消息时间片 超过进行流控***/
    private Integer startTimeSliceMapLimitSize = 200;
    /***落盘时间片大小 超过停止对账***/
    private Integer saveTimeSliceMapLimitSize = 10;
    private Integer pullKafkaMessageBackTime = 10 * 60 * 1000;
    /***运行实例***/
    private String serviceInstanceName = "upex-reconciliation-job01";
    /***忽律数据解析表达式 多个条件or连接***/
    private List<String> ignoreDataDecoderEl = new ArrayList<>();
    /***修复kafka消息数据有值覆盖***/
    private List<FixKafkaMessageData> repairKafkaMessageList = new ArrayList<>();
    /***合约待冻账初始差值***/
    //private Map<String, BigDecimal> profitTransfersInitValue = new HashMap<>();
    /***查询跨月动账数据***/
    private Boolean queryCrossMonthTransfer = false;
    /***kafka时间片消息延迟时间 默认1分钟***/
    private Long kafkaTimeSliceMessageDelayTime = 1 * 60 * 1000L;

    /***负值检测延迟时间***/
    private Long checkNegativeDelayTime = 0L;
    /***对账持仓日志打印***/
    private Boolean isOpenCheckSymbolUserPosition = false;
    /***回滚数据删除盈亏数据***/
    private Boolean deleteContractProfitForRollBackData = false;
    /***余额change值配置 目前用于otc***/
    private List<String> addBalanceChangeKeys = new ArrayList<>();
    /***余额change值配置 目前用于otc***/
    private List<String> subBalanceChangeKeys = new ArrayList<>();
    /***冻结change值配置 目前用于otc***/
    private List<String> addFreezeChangeKeys = new ArrayList<>();
    /***冻结change值配置 目前用于otc***/
    private List<String> subFreezeChangeKeys = new ArrayList<>();
    private boolean compareCoinPropertyNewOldSwitch = false;
    private boolean compareCoinPropertyOnlyOldSwitch = false;
    /***用户资产清除时间， 毫秒 默认 10小时 10 * 60***/
    private Long coinUserPropertyCleanTimeMinute = 600L;
    /***动账手续费类型***/
    private String profitTransferFeeCoinType = "";
    /***动账手续费冲销类型***/
    private String profitTransferFeeRecycleCoinType = "";
    /***利息动账手续费类型***/
    private String profitTransferInterestFeeCoinType = "";
    /***利息动账手续费冲销类型***/
    private String profitTransferInterestFeeRecycleCoinType = "";
    /***入出对账是否开启带动账检测逻辑***/
    private Boolean inOutCheckBillUnProfitTransferOpen = false;
    /***入出对账是否开启合约利息带动账检测逻辑***/
    private Boolean inOutCheckInterestUnProfitTransferOpen = false;
    /***时间片负值检测开关***/
    private boolean timeSliceNegativeOpen = false;
    /***时间片负值告警开关***/
    private boolean timeSliceNegativeAlarmOpen = true;
    /***时间片用户维度负值检测开关 如果关闭 表示进行userCoin维度负值检测***/
    private boolean timeSliceUserAssetsNegativeOpen = false;
    /***负值检测白名单***/
    private Set<Long> negativeUserWhiteList = new HashSet<>();
    /***负值检测阈值 用户#-1，金额***/
    private Map<String, BigDecimal> negativeUserThresholdValue = new HashMap<>();
    /***负值检测阈值 用户: {-1(全币种): -1000, 币种2: 金额2}***/
    private Map<Long, Map<Integer, BigDecimal>> negativeUserThresholdValueV2 = new HashMap<>();
    /***系统账户负值检测 "SPOT_RECON_FEE#币种(-1全币种)": 1000***/
    private Map<String, BigDecimal> negativeSystemUserThresholdValue = new HashMap<>();
    /***系统账户负值检测 "SPOT_RECON_FEE: {-1(全币种):金额1, 币种2: 金额2}"***/
    private Map<String, Map<Integer, BigDecimal>> negativeSystemUserThresholdValueV2 = new HashMap<>();
    /***负值检测打印明细日志***/
    private Boolean negativeCheckPrintDetailLog = false;
    /***负值告警默认金额***/
    private BigDecimal negativeCheckDefaultThresholdValue = BigDecimal.ZERO;
    /***用户资产负值检测开关***/
    private boolean userAssetCheckNegativeOpen = false;
    /***汇率获取时间差, 默认60s***/
    private Integer priceTimeDiff = 60;
    /***缓存中历史负值告警时间阈值，默认20分钟，大于此阈值，则不告警***/
    private Long cacheNegativeAlarmTimeThreshold = 20 * 60 * 1000L;
    /***缓存中历史负值告警时间间隔，默认10分钟***/
    private Long cacheNegativeAlarmTimeInterval = 10 * 60 * 1000L;
    /***缓存中历史负值超时自动重跑，默认60分钟一次***/
    private Long cacheNegativeAutoRepairInterval = 60 * 60 * 1000L;
    /***内部划转检测开关***/
    private boolean reconInnerTransferOpen = false;
    /***计算增量充提订单开关***/
    private boolean calculateIncrOrderOpen = false;
    /***存盘查询用户资产并发线程数***/
    private Integer saveDataQueryUserCoinConcurrence = 20;
    /***理财独有-本金和派息业务类型 包含 FINANCIAL_PRINCIPAL_CHANGE_FIN_BIZ_TYPE_LIST SETTLE_INTEREST_FIN_BIZ_TYPE_LIST ***/
    private Set<String> principalAndSettleInterestFinBizTypeList = Sets.newHashSet("101", "102", "201", "202", "301", "302", "401", "402", "501", "502", "601", "602", "701", "702", "801", "802", "901", "1001", "1002", "1101", "1102", "1201", "1202", "1301", "1302", "1401", "1402", "503", "902", "606", "705", "1005", "607", "706", "204", "304", "404", "505", "604", "704", "804", "904", "1004", "1104", "1204", "1304", "1404", "1504", "1601", "1602", "608", "1701", "1702", "1704","1801", "1802");
    /***理财独有-纯本金变动 FINANCIAL_DEDUCT_PRINCIPAL_BIZ_TYPE_LIST***/
    //private Set<String> financialPrincipalChangeFinBizTypeList = Sets.newHashSet("101", "102", "201", "202", "301", "302", "401", "402", "501", "502", "601", "602", "701", "702", "801", "802", "901", "1001", "1002", "1101", "1102", "1201", "1202", "1301", "1302", "1401", "1402", "503", "902", "606", "705", "1005", "607", "706", "1601", "1602");
    /***理财独有-派息***/
    private Set<String> settleInterestFinBizTypeList = Sets.newHashSet("204", "304", "404", "505", "604", "704", "804", "904", "1004", "1104", "1204", "1304", "1404", "1504", "1704");
    /***理财独有-罚息***/
    private Set<String> financialDeductPrincipalBizTypeList = Sets.newHashSet("606", "705", "1005", "607", "706");
    /***理财独有-现货理财币种不一样业务类型 理财出-》现货入 现货USDT-理财BTC***/
    private Set<String> financialSpotDiffSettleFinBizTypeList = Sets.newHashSet("503", "902","1802");
    /***理财独有-结算币种一致业务类型***/
    private Set<String> financialSpotSameSettleFinBizTypeList = Sets.newHashSet("101", "102", "201", "202", "301", "302", "401", "402", "501", "502", "601", "602", "701", "702", "801", "802", "901", "1001", "1002", "1101", "1102", "1201", "1202", "1301", "1302", "1401", "1402", "1601", "1602", "608", "1701", "1702","1801");
    /***用户持仓数据发送kafka时间***/
    private Set<String> sendKafkaUserPositionTime = Sets.newHashSet("08:00:00", "00:00:00");
    /***用户资产增量kafka发送开关***/
    private Boolean sendKafkaUserIncrementSyncTopicOpen = false;
    /***用户资产快照kafka发送开关***/
    private Boolean sendKafkaUserSnapshotSyncTopicOpen = false;
    /***用户持仓kafka发送开关***/
    private Boolean sendKafkaUserPositionSyncTopicOpen = false;


    /**
     * 个人对账白名单
     */
    private List<Long> checkAssetsWhiteList = new ArrayList<>();


    /**
     * 下架的币种
     */
    private List<Integer> coinIdsEliminated;

    /**
     * 是否开启批量查询用户资产，默认：false
     */
    private boolean batchQueryUserAssetsOpen = false;
    /**
     * 批量查询用户资产，多少个userId查一次
     */
    private Integer batchQueryUserAssetsSize = 1;

    /**
     * 过滤用户资产为0的数据
     */
    private Boolean filterUserAssetForZeroOpen = false;

    /**
     * 提币检测盈利 入出类型
     */
    private Set<String> checkProfitInOutBizType = new HashSet<>();
    /**
     * 盈利增量流水是否显示日志
     */
    private Boolean incrFlowsProfitShowLogOpen = true;
    /***现货排除用户***/
    private Set<Long> kafkaExcludeUserIds = new HashSet<>();
    /***现货排除用户-kafka切割时间***/
    private Long kafkaExcludeTimestamp = null;
    /***现货排除数据库***/
    private String kafkaExcludeDatabase;
    /***重启热加载多少分钟之前的用户***/
    private Integer rebootLoadUserLastMinute = -60;
    /***kafka更新消息白名单，不在更新名单中的字段提币阻断***/
    private Set<String> kafkaUpdateColumnWhiteList = Sets.newHashSet("biz_time", "create_time", "created_date", "update_date");
    /***合约利息手续费业务类型***/
    private String contractInterestDealFeeBizType = "settle_interest";
    /***待动账校验开关***/
    private boolean timeSliceProfitTransferCheckOpen = false;
    /***kafka配置***/
    private ReconKafkaOpsConfig reconKafkaOpsConfig;
    /***业务线延迟存盘时间***/
    private String businessDalySaveTime = "Minute:10";
    /***kafka更新消息白名单，需要走对账逻辑***/
    private Set<String> kafkaUpdateMessageWhiteList = new HashSet<>();
    /***Kafka消息解析日志白名单***/
    private Set<Long> kafkaDecoderLogMsgIds = new HashSet<>();
    /***kafka丢弃消息白名单，需要走对账逻辑***/
    private Set<Long> kafkaAbandonMessageBlackList = new HashSet<>();
    /***时间片计算持仓打印日志用户***/
    private Set<Long> timeSliceUserPositionLogUserIds = new HashSet<>();
    /***时间片计算持仓打印日志时间片***/
    private Set<Long> timeSliceUserPositionLogTimes = new HashSet<>();
    /***模拟盘个人对账开关***/
    private boolean checkPapTradingFlowOpen = false;
    /***是否是模拟盘环境***/
    private boolean papTradingEnvironment = false;
    /***加载用户资产是否校验用户存在***/
    private boolean reloadUserAssetsForceCheckUserExist = false;
    /***内部提币类型***/
    private Set<String> innerWithdrawTypeSet = new HashSet<>();
    /***内部充值类型***/
    private Set<String> innerRechargeTypeSet = new HashSet<>();
    /***理财bgsol提币类型***/
    private Set<String> financeBgsolWithdrawTypeSet = new HashSet<>();
    /***内部转账取模 默认10个，可以修改***/
    private Integer innerTransferMod = 10;
    /***redis过期时间***/
    private long redisExpireMinutes = 30;
    /***redis过期时间***/
    private long financeBgsolRedisExpireMinutes = 60 * 24;

    /***业务线是否计算总账多空仓和盈亏数据***/
    private boolean sumLedgerPositionCountAndProfitFlag = false;
    /***汇总业务线动账手续费数据***/
    private boolean sumAccountTransferFeeFlag = false;
    /***汇总业务线合约利息动账手续费数据***/
    private boolean sumAccountTransferInterestFeeFlag = false;
    /***业务线入出对账动账手续费数据***/
    private boolean inoutCheckAccountTransferFeeFlag = false;
    /***业务线入出对账合约利息动账手续费数据***/
    private boolean inoutCheckAccountTransferInterestFeeFlag = false;
    /***业务线orderNoBizTime缓存过期时间***/
    private String orderNoBizTimeCacheExpireTime = "Minute:10";
    /***是否开启修复orderNoBizTime***/
    private boolean repairOrderNoBizTimeFlag = false;
    /***打印时间片仓位信息***/
    private boolean printTimeSliceUserPositionLogFlag = false;
    /*** 统一账户上线 兼容盈亏/换汇动账初始值，如果对账时间=配置时间 lastProfitCount - 初始值***/
    private Long billProfitCoinDetailTransferInitTime = 1737639600000l;
    /***异步加载用户查询三方资产***/
    private boolean asyncLoadUserQueryUserAssets = true;
    /***动账收费功能开关***/
    private boolean transferFeeOpen = false;
    /***手续费应收类型 通用***/
    private Set<String> transferFeeReceivableBizType = Sets.newHashSet();
    /***统一账户合约手续费账户***/
    private Long systemUtaContractFeeUserId;
    /***手续费动账实收类型 通用***/
    private Set<String> transferFeeReceivedBizType = Sets.newHashSet();
    /***重算仓位盈亏用户id***/
    private Set<Long> recalculatePositionProfitUserIdSet = new HashSet<>();
    /***不重算仓位币对 修改次参数注意配合总账参数一起修改***/
    private Set<String> notRecalculatePositionProfitSymbolSet = new HashSet<>();
    /***打印重算持仓明细日志***/
    private boolean recalculatePositionProfitLogOpen = false;
    /***存盘开关 等待总账对账成功***/
    private Boolean saveDataWaitLedgerCheckOk = true;
    /***用户资产不存在 默认流水资产 兼容onchain***/
    private boolean loadCoinUserNoExistDefaultBillAssets = false;
    /*** 负值系统用户类型 ***/
    private String neSystemAccountType;

    /***用户实时计算增加日志白名单用户***/
    private Set<Long> userProfitLogUserIds = new HashSet<>();
    /***用户实时盈亏重算小时 默认***/
    private Integer userProfitRecalculateHour = 30;
    /****用户盈亏计算保存redis并发线程数****/
    private Integer userProfitPushRedisConcurrence = 10;
    /***用户盈亏计算回退时间 基于对账时间***/
    private Integer userProfitPullKafkaMessageBackTime = 10 * 60 * 1000;
    /***用户盈亏计算回退小时 基于当前时间***/
    private Integer userProfitPullKafkaMessageBackHour = -30;

    /**
     * 合并时间片秒
     *
     * @return
     */
    public Long getMergeTimeSliceSecond() {
        return mergeTimeSliceSize * timeSliceSize;
    }

    /***业务线存盘 用户资产缓存id***/
    private String businessCoinUserIdCacheExpireTime = "DAY:2";
}
