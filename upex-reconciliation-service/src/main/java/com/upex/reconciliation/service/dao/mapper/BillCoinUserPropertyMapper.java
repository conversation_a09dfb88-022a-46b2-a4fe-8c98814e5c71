package com.upex.reconciliation.service.dao.mapper;


import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.model.dto.MaxMinIdDTO;
import com.upex.reconciliation.service.model.dto.UserCoinIdDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository("billCoinUserPropertyMapper")
public interface BillCoinUserPropertyMapper {

    int batchInsert(@Param("records") List<BillCoinUserProperty> records,
                    @Param("accountType") Byte accountType,
                    @Param("accountParam") String accountParam);

    int batchInsertIgnore(@Param("records") List<BillCoinUserProperty> records,
                          @Param("accountType") Byte accountType,
                          @Param("accountParam") String accountParam);

    Boolean deleteByCheckTime(@Param("accountType") Byte accountType, @Param("accountParam") String accountParam, @Param("checkTime") Date checkTime, @Param("pageSize") Long pageSize);

    Boolean updateById(@Param("accountType") Byte accountType, @Param("accountParam") String accountParam, @Param("data") BillCoinUserProperty data);

    BillCoinUserProperty selectById(@Param("accountType") Byte accountType, @Param("accountParam") String accountParam, @Param("id") Long id);


    int batchUpdate(@Param("list") List<BillCoinUserProperty> list,
                    @Param("accountType") Byte accountType,
                    @Param("accountParam") String accountParam);

    BillCoinUserProperty selectCheckByUidAndBusinessTime(@Param("fUid") Long fUid,
                                                         @Param("queryTime") Date queryTime,
                                                         @Param("accountParam") String accountParam,
                                                         @Param("accountType") Integer accountType);

    BillCoinUserProperty selectBySingleUserIdLatest(@Param("fUid") Long fUid,
                                                    @Param("accountParam") String accountParam,
                                                    @Param("accountType") Integer accountType);


    Boolean batchDelete(@Param("beginId") Long beginId, @Param("pageSize") Long pageSize, @Param("accountType") Byte accountType, @Param("accountParam") String accountParam);


    List<BillCoinUserProperty> selectCheckForTheResults(@Param("fUid") Long fUid,
                                                        @Param("accountParam") String accountParam,
                                                        @Param("accountType") Integer accountType);


    List<BillCoinUserProperty> selectByIds(
            @Param("checkTime") Date checkTime,
            @Param("coinId") Integer coinId,
            @Param("uids") List<Long> uids,
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam
    );


    List<BillCoinUserProperty> selectByUserIdsAndCheckTime(@Param("userIds") List<Long> userIds,
                                                           @Param("checkTime") Date checkTime,
                                                           @Param("accountType") Byte accountType,
                                                           @Param("accountParam") String accountParam);


    int updateCheckTimeById(@Param("ids") List<Long> ids,
                            @Param("checkTime") Date checkTime,
                            @Param("updateTime") Date updateTime,
                            @Param("accountType") Byte accountType,
                            @Param("accountParam") String accountParam);


    List<BillCoinUserProperty> selectAllAssetsTiming(@Param("accountType") Integer accountType,
                                                     @Param("accountParam") String accountParam,
                                                     @Param("checkTime") String checkTime);


    List<Long> selectUserIds(@Param("accountType") Integer accountType,
                             @Param("accountParam") String accountParam);


    List<BillCoinUserProperty> selectSysAssetsSnapShotByTime(@Param("userId") Long userId, @Param("checkTime") Date checkTime, @Param("accountType") Integer accountType,
                                                             @Param("accountParam") String accountParam);

    List<BillCoinUserProperty> selectSingleUser(@Param("userId") Long userId,
                                                @Param("coinId") Integer coinId,
                                                @Param("accountType") Integer accountType,
                                                @Param("accountParam") String accountParam,
                                                @Param("checkTime") Date checkTime);

    /**
     * 根据对帐时间获取用户的所有币种流水记录，可以后续添加币种作为参数
     *
     * @param userId
     * @param checkTime
     * @param accountType
     * @param accountParam
     * @return
     */
    List<BillCoinUserProperty> selectInfosByCheckTime(@Param("userId") Long userId,
                                                      @Param("checkTime") Date checkTime,
                                                      @Param("accountType") Integer accountType,
                                                      @Param("accountParam") String accountParam);


    /**
     * @param userId
     * @param coinIds
     * @param accountType
     * @param accountParam
     * @return
     */
    List<BillCoinUserProperty> selectByUserAndCoinIds(@Param("userId") Long userId,
                                                      @Param("coinIds") List<Integer> coinIds,
                                                      @Param("accountType") Integer accountType,
                                                      @Param("accountParam") String accountParam);

    /**
     * 获取最近次对帐时间
     *
     * @param userId
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    BillCoinUserProperty selectTime(@Param("userId") Long userId,
                                    @Param("accountType") Integer accountType,
                                    @Param("accountParam") String accountParam,
                                    @Param("checkTime") Date checkTime);


    /**
     * 删除初始化时间在重置对账时间之后的记录
     *
     * @param resetCheckTime
     * @param accountType
     * @param accountParam
     * @return
     */
    int deleteInitialTimeAfterRecord(@Param("resetCheckTime") Date resetCheckTime,
                                     @Param("accountType") Integer accountType,
                                     @Param("accountParam") String accountParam);

    /**
     * 更新重置对账时间在初始化时间和最后对账时间之间的记录
     *
     * @param userId
     * @param resetCheckTime
     * @param accountType
     * @param accountParam
     * @return
     */
    int updateInitAndCheckBetweenRecord(@Param("userId") Long userId, @Param("lastCheckTime") Date lastCheckTime, @Param("resetCheckTime") Date resetCheckTime,
                                        @Param("accountType") Integer accountType,
                                        @Param("accountParam") String accountParam);

    /**
     * 获取用户的快照时间的所有币种的对帐记录；
     * 参数取等号
     *
     * @param userId
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    List<BillCoinUserProperty> selectRecordsByUserIdAndCheckTime(
            @Param("userId") Long userId,
            @Param("accountType") String accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime);

    /**
     * 获取用户下不同币种的时间快照
     *
     * @param userId
     * @param coinId
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    BillCoinUserProperty selectRecordLimitByUserCoin(
            @Param("userId") Long userId,
            @Param("coinId") Integer coinId,
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime
    );

    /**
     * 获取用户下币种的快照记录（对帐时间一直更新，初始化时间不变）
     *
     * @param userId
     * @param coinId
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    BillCoinUserProperty selectCheckTimeOverInitLimitByUerCoin(
            @Param("userId") Long userId,
            @Param("coinId") Integer coinId,
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime);

    /**
     * 获取对帐时间记录的的所有币种
     *
     * @param userId
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    List<BillCoinUserProperty> selectCoinsByUserAndTime(
            @Param("userId") Long userId,
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("checkTime") Date checkTime
    );

    /**
     * 删除历史记录
     *
     * @param ids          主键集合
     * @param accountType  账户类型
     * @param accountParam 账户参数
     * @return
     */
    int deleteUserCoinHistoryRecord(@Param("accountType") String accountType,
                                    @Param("accountParam") String accountParam,
                                    @Param("ids") List<Long> ids);

//    /**
//     * 查询历史记录
//     *
//     * @param queryUserDTO 查询用户资产dto 根据用户分组的数据查询
//     * @return
//     */
//    List<Long> selectUserCoinHistoryRecord(QueryUserDTO queryUserDTO);

    /**
     * 获取指定时间范围的数据
     *
     * @param accountType
     * @param accountParam
     * @param startTime
     * @param endTime
     * @param pageSize
     * @return
     */
    List<BillCoinUserProperty> selectRangeCheckTimeRecordPage(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("minId") Long minId,
            @Param("pageSize") Long pageSize);

    List<BillCoinUserProperty> selectRangeCheckTimeRecord(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime);


    List<BillCoinUserProperty> selectAll(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam);


    List<BillCoinUserProperty> selectUserLatestRecord(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("userId") Long userId,
            @Param("coinId") Integer coinId);


    List<BillCoinUserProperty> selectUserLatestAllRecord(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("userId") Long userId);

    List<BillCoinUserProperty> selectUserCoinIds(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("userId") Long userId,
            @Param("coinIds") List<Integer> coinIds);


    List<BillCoinUserProperty> selectAssetListByUserId(@Param("userId") Long userId,
                                                       @Param("accountType") Byte accountType,
                                                       @Param("accountParam") String accountParam);

    /**
     * 查询最新的用户资产
     *
     * @param userId
     * @param accountType
     * @param accountParam
     * @return {@link List< BillCoinUserProperty> }
     * <AUTHOR>
     * @date 2023/4/30 01:15
     */
    List<BillCoinUserProperty> selectLastUserAssetByUserId(@Param("userId") Long userId,
                                                           @Param("accountType") Byte accountType,
                                                           @Param("accountParam") String accountParam);

    BillCoinUserProperty selectUserCoinLatestRecord(
            @Param("accountType") Integer accountType,
            @Param("accountParam") String accountParam,
            @Param("userId") Long userId,
            @Param("coinId") Integer coinId,
            @Param("checkTime") Date checkTime);


    BillCoinUserProperty selectUserCoinRecord(
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam,
            @Param("userId") Long userId,
            @Param("coinId") Integer coinId);

    /**
     * 批量查询用户
     *
     * @param accountType
     * @param accountParam
     * @param records
     * @return
     */
    List<UserCoinIdDTO> selectByUserAndCoins(@Param("accountType") Byte accountType,
                                             @Param("accountParam") String accountParam,
                                             @Param("records") List<BillCoinUserProperty> records);

    /**
     * 查询最近活跃用户id
     *
     * @param accountType
     * @param accountParam
     * @param startTime
     * @param startOffset
     * @param pageSize
     * @return
     */
    List<Long> selectLastUserIdByCheckTime(
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam,
            @Param("startTime") Date startTime,
            @Param("startOffset") Long startOffset,
            @Param("pageSize") Integer pageSize);

    /**
     * 时间数据条数
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    Long countByGteCheckTime(@Param("accountType") Byte accountType,
                             @Param("accountParam") String accountParam,
                             @Param("checkTime") Date checkTime);

    Cursor<Long> cursorSelectLastUserIdByCheckTime(
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam,
            @Param("startTime") Date startTime);

    /**
     * 获取对大id
     *
     * @param accountType
     * @param accountParam
     * @return
     */
    Long selectMaxId(@Param("accountType") Byte accountType,
                     @Param("accountParam") String accountParam);

    /**
     * 批量查询表记录
     *
     * @param accountType
     * @param accountParam
     * @param startId
     * @param endId
     * @return
     */
    List<BillCoinUserProperty> selectCoinUserByStartAndEndId(
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam,
            @Param("startId") Long startId,
            @Param("endId") Long endId);

    /**
     * 更新资产
     *
     * @param accountType
     * @param accountParam
     * @param record
     * @return
     */
    boolean updateSelectiveById(
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam,
            @Param("record") BillCoinUserProperty record);

    /**
     * 获取最小id
     *
     * @param accountType
     * @param accountParam
     * @return
     */
    Long selectMinId(@Param("accountType") Byte accountType,
                     @Param("accountParam") String accountParam);

    /**
     * 查询资产
     *
     * @param accountType
     * @param accountParam
     * @param minId
     * @param maxId
     * @return
     */
    List<BillCoinUserProperty> selectCoinUserByIdSegment(
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam,
            @Param("minId") Long minId,
            @Param("maxId") Long maxId);

    /**
     * 查询用户最小创建时间
     *
     * @param accountType
     * @param accountParam
     * @param startId
     * @param endId
     * @return
     */
    List<Map<String, Object>> selectMinCreateTimeBatch(@Param("accountType") Byte accountType,
                                                       @Param("accountParam") String accountParam, @Param("startId") Long startId, @Param("endId") Long endId);

    /**
     * 获取最大最小id
     *
     * @param accountType
     * @param accountParam
     * @return
     */
    MaxMinIdDTO selectMaxMinId(@Param("accountType") Byte accountType,
                               @Param("accountParam") String accountParam);

    /**
     * 更新用户资产
     *
     * @param accountType
     * @param accountParam
     * @param startId
     * @param endId
     * @return
     */
    Integer updatePropToSprop(
            @Param("accountType") Byte accountType,
            @Param("accountParam") String accountParam,
            @Param("startId") Long startId,
            @Param("endId") Long endId);

    /**
     * 更新sprop
     *
     * @param accountType
     * @param accountParam
     * @param data
     * @return
     */
    Boolean updateSpropById(@Param("accountType") Byte accountType,
                            @Param("accountParam") String accountParam,
                            @Param("data") BillCoinUserProperty data);

    /**
     * 根据userIds查询
     *
     * @param accountType
     * @param accountParam
     * @param userIds
     * @return
     */
    List<BillCoinUserProperty> selectCoinUserByUserIds(@Param("accountType") Byte accountType,
                                                       @Param("accountParam") String accountParam,
                                                       @Param("uids") List<Long> userIds);
}