package com.upex.reconciliation.service.model.config;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ReconOrderConfig {

    /**是否开启*/
    private boolean isOpen = true;
    /**对账系统类型*/
    private Byte orderType;
    /**开启订单分片*/
    private boolean dynamicShardingEnabled =false;
    /**订单流水类型**/
    private List<String> flowTypes=new ArrayList<>();
    /**用户流水类型**/
    private List<String> userBizTypes=new ArrayList<>();
    /**系统流水类型**/
    private List<String> systemBizTypes=new ArrayList<>();
   /**系统账号**/
    private Long systemUserId;
    /**订单过期时间，单位分钟*/
    private int orderExpireTime = 15;
    /**扫描超时订单**/
    private int scanTimeout = 5 * 60 * 1000;
    /** 重试次数 */
    private int retryCount = 2;
    /** 分片扫描范围，单位分钟 */
    private int shardScope = 30;
    /** 是否开启行情价计算*/
    private boolean enableQuotaPrice = true;
    /** 是否开启订单价格校验*/
    private boolean enableOrderPrice = true;
    /**利润价格和成本价低差异比率*/
    private String profitCostRate = "0.01";
    /**行情价最高阈值*/
    private String highThresholdRate = "0.1";
    /**行情价最低阈值*/
    private String lowThresholdRate = "0.1";
    /**执行实例**/
    private String serviceInstanceName = "upex-reconciliation-job03";
    /**订单过滤类型**/
    private List<String> orderFilterStatus = Lists.newArrayList("2");


}
