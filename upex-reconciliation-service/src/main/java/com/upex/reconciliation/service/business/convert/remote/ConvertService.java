package com.upex.reconciliation.service.business.convert.remote;

import com.upex.convert.facade.feign.inner.InnerConvertOrderFeignClient;
import com.upex.convert.facade.params.OrderVO;
import com.upex.utils.framework.ApiResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class ConvertService {

    // 测试环境使用
//    @Resource
//    private ConvertFeignService convertFeignService;

    @Resource
    private InnerConvertOrderFeignClient convertOrderFeignClient;

    /**
     * 获取单个闪兑订单
     */
    public OrderVO getConvertOrder(Long orderId,Long accountId) {
        ApiResult<OrderVO> orderVOApiResult = convertOrderFeignClient.queryOrder(accountId, orderId,null);
        return orderVOApiResult.getData() != null ? orderVOApiResult.getData() : null;
     }
}