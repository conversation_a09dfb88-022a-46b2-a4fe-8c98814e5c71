package com.upex.reconciliation.service.business.convert.task;

import com.upex.convert.facade.params.OrderVO;
import com.upex.reconciliation.service.business.convert.*;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderFailureEnum;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderStausEnum;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderTypeEnum;
import com.upex.reconciliation.service.business.convert.model.CombinedOrderData;
import com.upex.reconciliation.service.business.convert.model.ConvertBill;
import com.upex.reconciliation.service.business.convert.model.ConvertOrder;
import com.upex.reconciliation.service.business.convert.remote.ConvertService;
import com.upex.reconciliation.service.business.convert.remote.SpotBillService;
import com.upex.reconciliation.service.dao.entity.ReconOrderFailureRecord;
import com.upex.reconciliation.service.model.config.ReconOrderConfig;
import com.upex.reconciliation.service.service.ReconOrderFailureRecordService;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.spot.dto.result.bill.NewSpotBillInfoResult;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.business.convert.ReconOrderConstants.*;


@Component
@Slf4j
public class DelayReconOrderTask implements Runnable {

    @Resource
    private ReconOrderFailureRecordService reconOrderFailureRecordService;

    @Resource(name = "reconRedisTemplate")
    private RedisTemplate<String, Object> reconRedisTemplate;

    @Resource
    private ReconOrderDataProcessor orderDataProcessor;

    @Resource
    private ConvertService convertService;

    @Resource
    private SpotBillService spotBillService;

    private final DelayQueue<DelayedReconRecord> delayQueue = new DelayQueue<>();
    private final ExecutorService executor = Executors.newSingleThreadExecutor(r -> new Thread(r, "DelayOrderReconThread"));
    // 添加任务运行状态标志
    private volatile boolean isRunning = false;


    public void work() {
        if (!isRunning) {
            loadFailedRecordsOnStartup();
            executor.submit(this);
            isRunning = true;
            log.info("DelayReconOrderTask initialized and started");
        } else {
            log.info("DelayReconOrderTask is already running, skipping initialization");
        }
    }

    @PreDestroy
    public void destroy() {
        executor.shutdownNow();
        isRunning = false;
        log.info("DelayReconOrderTask shutdown");
    }

    /**
     * 启动时加载未处理的超时失败记录到延迟队列
     */
    private void loadFailedRecordsOnStartup() {
        log.info("Loading unprocessed failure records into delay queue...");
        // 加载状态为待处理(0)，失败类型为超时(2)的记录
        List<ReconOrderFailureRecord> failedRecords = reconOrderFailureRecordService.getUnprocessedRecords(ReconOrderTypeEnum.CONVERT.getCode(), ReconOrderStausEnum.NOT_RECONCILED.getCode()); // bizType=1 for convert
        if (CollectionUtils.isNotEmpty(failedRecords)) {
            for (ReconOrderFailureRecord record : failedRecords) {
                addRecordToQueue(record, 10);
            }
            log.info("Loaded {} unprocessed failure records.", failedRecords.size());
        }
    }

    /**
     * 将失败记录添加到延迟队列进行处理
     *
     * @param record         失败记录
     * @param delayInSeconds 延迟处理的秒数
     */
    public void addRecordToQueue(ReconOrderFailureRecord record, long delayInSeconds) {
        if (record == null) return;
        // 检查任务是否已经在队列中
        for (DelayedReconRecord queuedRecord : delayQueue) {
            if (queuedRecord.getRecord().getOrderId().equals(record.getOrderId())) {
                log.info("Order {} already in delay queue, skipping.", record.getOrderId());
                return;
            }
        }
        long triggerTime = System.currentTimeMillis() + (delayInSeconds * 1000);
        delayQueue.offer(new DelayedReconRecord(record, triggerTime));
        log.info("Added order {} to delay queue for reprocessing in {} seconds.", record.getOrderId(), delayInSeconds);
    }

    @Override
    public void run() {
        log.info("DelayOrderReconTask started.");
        while (!Thread.currentThread().isInterrupted()) {
            try {
                DelayedReconRecord delayedRecord = delayQueue.take();
                processRecord(delayedRecord.getRecord());
            } catch (InterruptedException e) {
                log.warn("DelayOrderReconTask interrupted.");
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                log.error("Error processing delay queue", e);
            }
        }
    }

    private void processRecord(ReconOrderFailureRecord record) {
        try {
            log.info("Processing delay reconciliation record for order: {}", record.getOrderId());
            ReconOrderConfig reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
            Long orderId = record.getOrderId();
            Long accountId = record.getUserId();
            // 获取系统账户ID
            Long systemAccountId = reconOrderConfig.getSystemUserId();
            // 获取主订单数据
            OrderVO orderVO = convertService.getConvertOrder(orderId, accountId);
            if (orderVO == null) {
                log.warn("Main order not found for orderId: {}, accountId: {}", orderId, accountId);
                handleMissingData(record);
                return;
            }
            // 获取用户流水订单
            List<NewSpotBillInfoResult> userBills = spotBillService.getSpotBillByBizType(accountId, orderId,reconOrderConfig.getUserBizTypes());
            if (userBills == null || userBills.isEmpty()) {
                log.warn("User bills not found for orderId: {}, accountId: {}", orderId, accountId);
                handleMissingData(record);
                return;
            }
            // 获取系统流水订单
            List<NewSpotBillInfoResult> systemBills = spotBillService.getSpotBillByBizType(systemAccountId, orderId,reconOrderConfig.getSystemBizTypes());
            if (systemBills == null || systemBills.isEmpty()) {
                log.warn("System bills not found for orderId: {}, systemAccountId: {}", orderId, systemAccountId);
                handleMissingData(record);
                return;
            }

            // 验证数据完整性
            if (!validateDataCompleteness(reconOrderConfig, userBills, systemBills)) {
                log.warn("Data incomplete for order: {}, retrying...", orderId);
                handleMissingData(record);
                return;
            }

            // 组装CombinedOrderData
            CombinedOrderData combinedOrderData = buildCombinedOrderData(orderVO, userBills, systemBills, reconOrderConfig);
            if (combinedOrderData == null) {
                log.error("Failed to build combined order data for order: {}", orderId);
                handleMissingData(record);
                return;
            }
            // 执行对账处理
            handleReconciliation(record, combinedOrderData);
        } catch (Exception e) {
            log.error("Error processing delay reconciliation record for order: {}", record.getOrderId(), e);
            handleMissingData(record);
        }
    }

    private void handleMissingData(ReconOrderFailureRecord record) {
        String orderId = String.valueOf(record.getOrderId());
        log.warn("Data missing for order {}. Retrying...", orderId);

        int retryCount = (record.getRetryCount() == null ? 0 : record.getRetryCount()) + 1;
        // 更新数据库中的重试次数
        reconOrderFailureRecordService.updateRetryCount(record.getId(), retryCount);
        record.setRetryCount(retryCount); // 更新内存中的对象

        ReconOrderConfig reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
        if (retryCount >= reconOrderConfig.getRetryCount()) {
            log.error("ALERT: Order {} exceeded retry threshold ({}). Setting status to failed.", orderId, reconOrderConfig.getRetryCount());
            reconOrderFailureRecordService.updateStatus(record.getId(), ReconOrderStausEnum.IGNORED.getCode(), "system", "超过重试阈值");
        } else {
            // 使用指数退避策略重新入队 (1, 5, 10, 15... 分钟)
            long delay = (long) (60 * Math.pow(5, retryCount - 1));
            log.info("Re-queuing order {} for retry {} in {} seconds.", orderId, retryCount, delay);
            addRecordToQueue(record, delay);
        }
    }





    /**
     * 验证数据完整性
     */
    private boolean validateDataCompleteness(ReconOrderConfig reconOrderConfig,
                                             List<NewSpotBillInfoResult> userBills,
                                             List<NewSpotBillInfoResult> systemBills) {
        // 检查是否包含所有必需的业务类型
        List<String> requiredUserBizTypes = reconOrderConfig.getUserBizTypes();
        List<String> requiredSystemBizTypes = reconOrderConfig.getSystemBizTypes();

        // 验证用户流水是否包含所有必需的业务类型
        Set<String> userBizTypeSet = userBills.stream()
                .map(bill -> String.valueOf(bill.getBizType()))
                .collect(Collectors.toSet());

        for (String requiredType : requiredUserBizTypes) {
            if (!userBizTypeSet.contains(requiredType)) {
                log.warn("Missing required user biz type: {}", requiredType);
                return false;
            }
        }

        // 验证系统流水是否包含所有必需的业务类型
        Set<String> systemBizTypeSet = systemBills.stream()
                .map(bill -> String.valueOf(bill.getBizType()))
                .collect(Collectors.toSet());

        for (String requiredType : requiredSystemBizTypes) {
            if (!systemBizTypeSet.contains(requiredType)) {
                log.warn("Missing required system biz type: {}", requiredType);
                return false;
            }
        }

        return true;
    }

    /**
     * 组装CombinedOrderData对象
     */
    private CombinedOrderData buildCombinedOrderData(OrderVO orderVO,
                                                     List<NewSpotBillInfoResult> userBills,
                                                     List<NewSpotBillInfoResult> systemBills,
                                                     ReconOrderConfig reconOrderConfig) {
        try {
            // 转换主订单数据
            ConvertOrder mainOrder = convertOrderVOToConvertOrder(orderVO);
            if (mainOrder == null) {
                log.error("Failed to convert OrderVO to ConvertOrder for order: {}", orderVO.getId());
                return null;
            }
            // 创建CombinedOrderData对象
            CombinedOrderData combinedOrderData = new CombinedOrderData();
            combinedOrderData.addMainOrder(mainOrder);

            // 处理用户流水订单
            for (NewSpotBillInfoResult userBill : userBills) {
                ConvertBill convertBill = convertSpotBillToConvertBill(userBill);
                String bizType = String.valueOf(convertBill.getBillType());
                combinedOrderData.addFlowOrder(bizType, convertBill);
            }
            // 处理系统流水订单
            for (NewSpotBillInfoResult systemBill : systemBills) {
                ConvertBill convertBill = convertSpotBillToConvertBill(systemBill);
                String bizType = String.valueOf(systemBill.getBizType());
                combinedOrderData.addFlowOrder(bizType, convertBill);
            }
            log.info("Successfully built CombinedOrderData for order: {}, userBills: {}, systemBills: {}",
                    mainOrder.getOrderId(), userBills.size(), systemBills.size());
            return combinedOrderData;
        } catch (Exception e) {
            log.error("Error building CombinedOrderData for order: {}", orderVO.getId(), e);
            return null;
        }
    }

    /**
     * 将OrderVO转换为ConvertOrder
     */
    private ConvertOrder convertOrderVOToConvertOrder(OrderVO orderVO) {
        if (orderVO == null) {
            return null;
        }
        ConvertOrder convertOrder = new ConvertOrder();
        convertOrder.setOrderId(orderVO.getId());
        convertOrder.setFromCoinId(orderVO.getFromCoin());
        convertOrder.setFromCoinCount(new BigDecimal(orderVO.getFromCoinCount()));
        convertOrder.setToCoinId(orderVO.getToCoin());
        convertOrder.setToCoinCount(new BigDecimal(orderVO.getToCoinCount()));
        convertOrder.setStatus(orderVO.getStatus());
        convertOrder.setCreateTime(orderVO.getCreateTime());
        // 如果fromCoinId或者toCoinId是usdt
        if (orderVO.getFromCoin() == 2 || orderVO.getToCoin() == 2) {
            convertOrder.setPc1(new BigDecimal(orderVO.getQuotedPrice()));
            convertOrder.setPt1(new BigDecimal(orderVO.getQuotedPrice()));
        } else {
            convertOrder.setPc2(new BigDecimal(orderVO.getQuotedPrice()));
            convertOrder.setPt2(new BigDecimal(orderVO.getQuotedPrice()));
        }
        return convertOrder;
    }

    /**
     * 将NewSpotBillInfoResult转换为ConvertBill
     */
    private ConvertBill convertSpotBillToConvertBill(NewSpotBillInfoResult spotBill) {
        if (spotBill == null) {
            return null;
        }
        ConvertBill convertBill = new ConvertBill();
        convertBill.setOrderId(spotBill.getBizOrderId());
        convertBill.setBillType(String.valueOf(spotBill.getBizType()));
        convertBill.setAccountId(spotBill.getUserId());
        convertBill.setCoinId(spotBill.getCoinId());
        convertBill.setBalanceChange(spotBill.getBalanceChange()); // 余额变更
        return convertBill;
    }

    /**
     * 处理对账逻辑
     */
    private void handleReconciliation(ReconOrderFailureRecord record, CombinedOrderData combinedOrderData) {
        String orderId = String.valueOf(record.getOrderId());
        log.info("All data present for order {}. Attempting final reconciliation.", orderId);
        try {
            // 执行对账处理
            ReconOrderResult result = orderDataProcessor.processCombinedOrder(combinedOrderData);
            if (result.isSuccess()) {
                // 对账成功
                log.info("Reconciliation successful for order {}.", orderId);
                reconOrderFailureRecordService.updateStatus(record.getId(), (byte) 1, "system", "延迟对账成功");
                // 清理Redis中的相关数据（如果存在）
                cleanupRedisData(orderId);

            } else {
                // 对账失败
                String failureReason = String.format("对账失败: %s - %s",
                        result.getFailureType(), result.getFailureReason());
                log.error("Reconciliation failed for order {}: {}", orderId, failureReason);
                // 判断是否为可重试的错误
                if (isRetryableFailure(result.getFailureType())) {
                    // 可重试的错误，重新入队
                    handleMissingData(record);
                } else {
                    // 不可重试的错误，标记为最终失败
                    reconOrderFailureRecordService.updateStatus(record.getId(), ReconOrderStausEnum.RECONCILED_FAILURE.getCode(), "system", failureReason);
                }
            }

        } catch (Exception e) {
            log.error("Error during reconciliation for order {}", orderId, e);
            // 异常情况下重试
            handleMissingData(record);
        }
    }

    /**
     * 清理Redis中的订单数据
     */
    private void cleanupRedisData(String orderId) {
        try {
            // 删除主订单数据
            String mainOrderKey = MAIN_ORDER_PREFIX + orderId;
            reconRedisTemplate.delete(mainOrderKey);

            // 删除流水订单数据
            ReconOrderConfig reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
            List<String> flowOrderKeys = new ArrayList<>();
            for (String bizType : reconOrderConfig.getFlowTypes()) {
                flowOrderKeys.add(FLOW_ORDER_PREFIX + bizType + ":" + orderId);
            }
            if (!flowOrderKeys.isEmpty()) {
                reconRedisTemplate.delete(flowOrderKeys);
            }

            // 删除索引数据
            if (reconOrderConfig.isDynamicShardingEnabled()) {
                // 动态分片情况下需要计算分片key
                String shardKey = MAIN_ORDER_TIMESTAMP_PREFIX +
                        LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
                reconRedisTemplate.opsForZSet().remove(shardKey, orderId);
            } else {
                reconRedisTemplate.opsForZSet().remove(DEFAULT_MAIN_ORDER_INDEX_KEY, orderId);
            }

            log.info("Cleaned up Redis data for order: {}", orderId);

        } catch (Exception e) {
            log.warn("Failed to cleanup Redis data for order: {}", orderId, e);
            // 清理失败不影响对账结果
        }
    }

    /**
     * 判断是否为可重试的失败类型
     */
    private boolean isRetryableFailure(ReconOrderFailureEnum failureType) {
        if (failureType == null) {
            return true; // 未知错误默认可重试
        }

        switch (failureType) {
            case TIMEOUT_FLOW:
            case TIMEOUT_MAIN:
            case UNKNOWN_ERROR:
                return true; // 超时和未知错误可重试
            case INCONSISTENT_BALANCE_CHANGE:
            case INCONSISTENT_COIN_COUNT:
            case INCONSISTENT_PRICE_DATA:
            case INCONSISTENT_QUOTA_PRICE:
            case PRICE_DATA_MISSING:
                return false; // 数据不一致错误不可重试
            default:
                return true; // 默认可重试
        }
    }

    /**
     * 用于DelayQueue的内部包装类
     */
    @Getter
    private static class DelayedReconRecord implements Delayed {
        private final ReconOrderFailureRecord record;
        private final long triggerTime;

        public DelayedReconRecord(ReconOrderFailureRecord record, long triggerTime) {
            this.record = record;
            this.triggerTime = triggerTime;
        }

        @Override
        public long getDelay(TimeUnit unit) {
            long diff = triggerTime - System.currentTimeMillis();
            return unit.convert(diff, TimeUnit.MILLISECONDS);
        }

        @Override
        public int compareTo(Delayed o) {
            return Long.compare(this.triggerTime, ((DelayedReconRecord) o).triggerTime);
        }
    }
}
