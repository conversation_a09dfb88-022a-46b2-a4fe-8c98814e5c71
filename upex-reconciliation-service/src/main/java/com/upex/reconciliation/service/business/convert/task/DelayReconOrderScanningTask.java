package com.upex.reconciliation.service.business.convert.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderFailureEnum;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderStausEnum;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderTypeEnum;
import com.upex.reconciliation.service.dao.entity.ReconOrderFailureRecord;
import com.upex.reconciliation.service.model.config.ReconOrderConfig;
import com.upex.reconciliation.service.service.ReconOrderFailureRecordService;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

import static com.upex.reconciliation.service.business.convert.ReconOrderConstants.*;


@Component
@Slf4j
public class DelayReconOrderScanningTask {

    @Resource
    private ReconOrderFailureRecordService reconOrderFailureRecordService;

    @Resource
    private DelayReconOrderTask delayOrderReconTask;

    @Resource(name = "reconRedisTemplate")
    private RedisTemplate<String, Object> reconRedisTemplate;
    private final ReconOrderConfig reconOrderConfig;

    public DelayReconOrderScanningTask() {
        this.reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
    }

    /**
     * 扫描延迟订单（
     */
    public void scanDelayOrders() {
        try {
            List<String> shardKeys = getShardKeysToScan();
            for (String shardKey : shardKeys) {
                // 扫描超时的订单
                long minutesAgo = (System.currentTimeMillis() - reconOrderConfig.getScanTimeout()) / 1000;
                Set<Object> orderIds = reconRedisTemplate.opsForZSet().rangeByScore(shardKey, 0, minutesAgo);
                if (orderIds == null || orderIds.isEmpty()) {
                    continue;
                }
                orderIds.forEach(e -> {
                    Long orderId = Long.valueOf(e.toString());
                    // 先查库，存在就忽略
                    ReconOrderFailureRecord existingRecord = reconOrderFailureRecordService.getByOrderId(orderId);
                    if (existingRecord != null) {
                        log.info("Order {} already has a failure record, skipping creation.", orderId);
                        return;
                    }
                    // 创建并保存失败记录
                    ReconOrderFailureRecord failureRecord = createFailureRecord(orderId);
                    boolean saved = reconOrderFailureRecordService.recordFailure(failureRecord);
                    if (saved) {
                        // 获取带有DB生成ID的记录，以便后续更新
                        ReconOrderFailureRecord savedRecord = reconOrderFailureRecordService.getByOrderId(orderId);
                        if (savedRecord != null) {
                            // 添加到延迟队列，10秒后开始重试
                            delayOrderReconTask.addRecordToQueue(savedRecord, 10);
                        } else {
                            log.error("Could not fetch saved failure failureRecord for orderId {}", orderId);
                        }
                        // 主订单索引中移除超时订单
                        reconRedisTemplate.opsForZSet().remove(shardKey, e.toString());
                    } else {
                        log.error("Failed to save failure failureRecord for timeout order {}", orderId);
                    }
                });
            }
        } catch (Exception e) {
            log.error("Error scanning delay orders", e);
        }
    }

    /**
     * 获取需要扫描的分片key
     */
    private List<String> getShardKeysToScan() {
        List<String> shardKeys = new ArrayList<>();
        if (reconOrderConfig.isDynamicShardingEnabled()) {
            LocalDateTime now = LocalDateTime.now();
            for (int i = 0; i <= reconOrderConfig.getShardScope(); i++) {
                LocalDateTime shardTime = now.minusMinutes(i);
                shardKeys.add(MAIN_ORDER_TIMESTAMP_PREFIX + shardTime.format(SHARD_FORMATTER));
            }
        } else {
            shardKeys.add(DEFAULT_MAIN_ORDER_INDEX_KEY);
        }
        return shardKeys;
    }

    /**
     * 创建延迟订单失败记录
     */
    private ReconOrderFailureRecord createFailureRecord(Long orderId) {

        String mainOrderKey = MAIN_ORDER_PREFIX + orderId;
        String raw = null;
        Object order = reconRedisTemplate.opsForValue().get(mainOrderKey);
        Long accountId = null;
        if (Objects.nonNull(order)) {
            JSONObject jsonObject = JSON.parseObject(order.toString());
            accountId = jsonObject.getLong("accountId");
            raw = order.toString();
        }
        // 如果主订单获取不到，从流水单获取
        if (Objects.isNull(accountId)) {
            for (String bizType : reconOrderConfig.getFlowTypes()) {
                String flowOrderKey = FLOW_ORDER_PREFIX + bizType + ":" + orderId;
                Object bill = reconRedisTemplate.opsForValue().get(flowOrderKey);
                if (Objects.nonNull(bill)) {
                    JSONObject jsonObject = JSON.parseObject(bill.toString());
                    accountId = jsonObject.getLong("accountId");
                    raw = bill.toString();
                    break;
                }
            }
        }
        ReconOrderFailureEnum failureEnum = Objects.nonNull(order) ?
                ReconOrderFailureEnum.TIMEOUT_FLOW : ReconOrderFailureEnum.TIMEOUT_MAIN;
        return ReconOrderFailureRecord.builder()
                .bizId(orderId)
                .orderId(orderId)
                .userId(accountId)
                .orderType(ReconOrderTypeEnum.CONVERT.getCode())
                .failureType(failureEnum.toString())
                .failureReason(failureEnum.getMessage())
                .status(ReconOrderStausEnum.NOT_RECONCILED.getCode()) // 未处理
                .rawData(raw)
                .createTime(new Date())
                .updateTime(new Date())
                .build();
    }
}
