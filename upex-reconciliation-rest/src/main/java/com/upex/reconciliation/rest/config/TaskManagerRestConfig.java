package com.upex.reconciliation.rest.config;

import com.upex.reconciliation.service.common.thread.BaseAsyncTaskManager;
import com.upex.utils.task.TaskManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @org.s,date 2020/3/11 2:43 PM
 */
@Configuration
public class TaskManagerRestConfig {
    @Bean("taskManager")
    public TaskManager getTaskManager() {
        return new TaskManager("taskManager", 100, 100, 10);
    }

    @Bean(name = "assetsTaskManager")
    public TaskManager getAssetsTaskManager() {
        return new TaskManager("assetsTaskManager", 1, 1, 1);
    }

    @Bean(name = "deleteTaskManager")
    public TaskManager getDeleteTaskManager() {
        return new TaskManager("deleteTaskManager", 1, 1, 1);
    }

    @Bean(name = "statisticsTaskManager")
    public TaskManager getStatisticsTaskManager() {
        return new TaskManager("statisticsTaskManager", 1, 1, 1);
    }

    @Bean(name = "assetSnapshotTaskManager")
    public TaskManager getAssetSnapshotTaskManager() {
        return new TaskManager("assetSnapshotTaskManager", 20, 20, 10);
    }

    @Bean(name = "batchAssetSnapshotTaskManager")
    public TaskManager getBatchAssetSnapshotTaskManager() {
        return new TaskManager("batchAssetSnapshotTaskManager", 100, 100, 10);
    }

    @Bean(name = "billBusinessTaskManager")
    public BaseAsyncTaskManager getBillBusinessTaskManager() {
        return new BaseAsyncTaskManager("billBusinessTaskManager", 10, 20, 100);
    }

    @Bean(name = "coinTaskManager")
    public TaskManager getCoinTaskManager() {
        return new TaskManager("coinTaskManager", 200, 200, 10);
    }

    @Bean(name = "coinInfoTaskManager")
    public TaskManager getCoinInfoTaskManager() {
        return new TaskManager("coinInfoTaskManager", 200, 200, 10);
    }

    @Bean(name = "changeUserTaskManager")
    public TaskManager getChangeUserTaskManager() {
        return new TaskManager("changeUserTaskManager", 1, 1, 1);
    }

    @Bean(name = "timeSliceTaskManager")
    public TaskManager getTimeSliceTaskManager() {
        return new TaskManager("timeSliceTaskManager", 1, 1, 1);
    }

    @Bean(name = "businessTaskManager")
    public TaskManager getBusinessTaskManager() {
        return new TaskManager("businessTaskManager", 1, 1, 1);
    }

    @Bean(name = "commonDeleteTaskManager")
    public TaskManager getCommonDeleteTaskManager() {
        return new TaskManager("commonDeleteTaskManager", 1, 1, 1);
    }

    @Bean(name = "coinAssetsTaskManager")
    public TaskManager getCoinAssetsTaskManager() {
        return new TaskManager("coinAssetsTaskManager", 200, 200, 10);
    }

    @Bean(name = "flowEngineTaskManager")
    public TaskManager getFlowEngineTaskManager() {
        return new TaskManager("flowEngineTaskManager", 1, 1, 1);
    }

    @Bean(name = "baseAsyncTaskManager")
    public BaseAsyncTaskManager getBaseAsyncTaskManager() {
        return new BaseAsyncTaskManager("baseAsyncTaskManager", 50, 200, 500);
    }

    @Bean("ruleEngineAsyncTaskManager")
    public BaseAsyncTaskManager getRuleEngineAsyncTaskManager() {
        return new BaseAsyncTaskManager("ruleEngineTaskManager", 1, 1, 100);
    }

    @Bean("negativeAutoRepairTaskManager")
    public BaseAsyncTaskManager getNegativeAutoRepairTaskManager() {
        return new BaseAsyncTaskManager("negativeAutoRepairTaskManager", 1, 1, 1);
    }

    @Bean("asyncLoadUserTaskManager")
    public TaskManager asyncLoadUserTaskManager() {
        return new TaskManager("asyncLoadUserTaskManager", 1, 1, 1);
    }

    @Bean(name = "assetUserProfitTaskManager")
    public TaskManager assetUserProfitTaskManager() {
        return new TaskManager("assetUserProfitTaskManager", 50, 50, 20);
    }

    @Bean(name = "userRedisProfitTaskManager")
    public TaskManager userRedisProfitTaskManager() {
        return new TaskManager("userRedisProfitTaskManager", 50, 50, 20);
    }

    @Bean("userProfitRtTaskManager")
    public TaskManager userProfitRtTaskManager() {
        return new TaskManager("userProfitRtTaskManager", 1, 1, 1);
    }

    @Bean("userBeginAssetsTaskManager")
    public TaskManager userBeginAssetsTaskManager() {
        return new TaskManager("userBeginAssetsTaskManager", 1, 1, 1);
    }
}
