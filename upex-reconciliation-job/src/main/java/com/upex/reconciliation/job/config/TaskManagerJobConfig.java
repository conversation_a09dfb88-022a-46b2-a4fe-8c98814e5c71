package com.upex.reconciliation.job.config;

import com.upex.reconciliation.service.common.thread.BaseAsyncTaskManager;
import com.upex.utils.task.TaskManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/11/23
 */
@Configuration
public class TaskManagerJobConfig {
    @Bean("taskManager")
    public TaskManager getTaskManager() {
        return new TaskManager("taskManager", 100, 100, 10);
    }


    @Bean(name = "assetSnapshotTaskManager")
    public TaskManager getAssetSnapshotTaskManager() {
        return new TaskManager("assetSnapshotTaskManager", 1, 1, 1);
    }

    @Bean(name = "coinTaskManager")
    public TaskManager getCoinTaskManager() {
        return new TaskManager("coinTaskManager", 1, 1, 1);
    }

    @Bean(name = "coinInfoTaskManager")
    public TaskManager getCoinInfoTaskManager() {
        return new TaskManager("coinInfoTaskManager", 1, 1, 1);
    }


    @Bean(name = "coinAssetsTaskManager")
    public TaskManager getCoinAssetsTaskManager() {
        return new TaskManager("coinAssetsTaskManager", 1, 1, 1);
    }


    @Bean(name = "batchAssetSnapshotTaskManager")
    public TaskManager getBatchAssetSnapshotTaskManager() {
        return new TaskManager("batchAssetSnapshotTaskManager", 1, 1, 1);
    }

    @Bean(name = "businessTaskManager")
    public TaskManager getBusinessTaskManager() {
        return new TaskManager("businessTaskManager", 1, 1, 1);
    }

    @Bean(name = "baseAsyncTaskManager")
    public BaseAsyncTaskManager getBaseAsyncTaskManager() {
        return new BaseAsyncTaskManager("baseAsyncTaskManager", 1, 1, 1);
    }


    @Bean(name = "billBusinessTaskManager")
    public BaseAsyncTaskManager getBillBusinessTaskManager() {
        return new BaseAsyncTaskManager("billBusinessTaskManager", 1, 1, 1);
    }

    @Bean(name = "deleteTaskManager")
    public TaskManager getDeleteTaskManager() {
        return new TaskManager("deleteTaskManager", 10, 10, 5);
    }

    @Bean("ruleEngineAsyncTaskManager")
    public BaseAsyncTaskManager getRuleEngineAsyncTaskManager() {
        return new BaseAsyncTaskManager("ruleEngineTaskManager", 16, 16, 100);
    }

    @Bean("negativeAutoRepairTaskManager")
    public BaseAsyncTaskManager getNegativeAutoRepairTaskManager() {
        return new BaseAsyncTaskManager("negativeAutoRepairTaskManager", 10, 20, 50);
    }

    @Bean("asyncLoadUserTaskManager")
    public TaskManager asyncLoadUserTaskManager() {
        return new TaskManager("asyncLoadUserTaskManager", 50, 50, 50);
    }

    @Bean(name = "assetUserProfitTaskManager")
    public TaskManager assetUserProfitTaskManager() {
        return new TaskManager("assetUserProfitTaskManager", 1, 1, 1);
    }

    @Bean(name = "userRedisProfitTaskManager")
    public TaskManager userRedisProfitTaskManager() {
        return new TaskManager("userRedisProfitTaskManager", 50, 50, 20);
    }

    @Bean("userProfitRtTaskManager")
    public TaskManager userProfitRtTaskManager() {
        return new TaskManager("userProfitRtTaskManager", 100, 100, 10);
    }

    @Bean("userBeginAssetsTaskManager")
    public TaskManager userBeginAssetsTaskManager() {
        return new TaskManager("userBeginAssetsTaskManager", 1, 1, 1);
    }
}
